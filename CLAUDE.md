# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `npm run build` - Build production application 
- `npm run start` - Start production server
- `npm run lint` - Run ESLint (currently disabled in builds)
- `npx tsc --noEmit` - Check TypeScript errors without building

## Architecture Overview

This is a Next.js 15.2.3 admin dashboard for LLM analytics and user management with a simplified routing structure:

### Data Sources
- **Supabase**: ONLY for authentication and user sessions (no database operations)
- **N8N Webhooks**: ALL data operations including database CRUD, analytics, models, user actions via server-side webhook calls with raw SQL

### Authentication Flow
- `src/middleware.ts` provides global auth protection using Supabase SSR
- All routes redirect to `/login` if unauthenticated except auth routes
- Three Supabase client types in `src/lib/supabase.ts`:
  - `createClient()` - Client-side browser client
  - `createServerSupabaseClient()` - Server components (async cookies import)
  - `createMiddlewareSupabaseClient()` - Middleware with response handling

### Route Architecture
Uses Next.js App Router with simplified structure:
- `/login` - Authentication page with centered layout
- `/` - Main dashboard with sidebar layout
- `/analytics`, `/models`, `/users`, `/logs`, `/bucket`, `/sql-editor` - Direct routes with sidebar
- `api/n8n/` - Server-side webhook proxy routes
- `api/analytics/` - Analytics data processing routes
- `api/bucket/` - Cloudflare R2 file management routes
- `api/sql-execute/` - Direct SQL execution for admin interface

### N8N Integration Pattern
All data operations flow through direct JWT-authenticated webhook calls using raw SQL:

#### **Modern Direct Pattern (Recommended)**
Used by: `/api/models`, `/api/users`, `/api/logs`, `/api/analytics`, `/api/sql-execute`
- Direct `fetch()` calls to `CHHLAT_DB_WEBHOOK_URL` with JWT authentication
- Uses `createBearerToken()` from `@/lib/jwt` for authorization headers
- Parameterized SQL with `$1, $2, $3` placeholders for security
- Server-side caching via `serverCache` from `@/lib/cache` (analytics & logs only)
- SQL comment filtering for `/api/sql-execute` (removes `--` comments before execution)

#### **Legacy Utility Pattern (Deprecated)**
- `src/lib/webhooks.ts` - Contains old webhook utility functions (being phased out)
- `WebhookCache` class - Legacy cache implementation (replaced by `ServerCache`)

#### **Implementation Examples**
```typescript
// Modern direct pattern
const bearerToken = createBearerToken()
const response = await fetch(process.env.CHHLAT_DB_WEBHOOK_URL, {
  method: 'POST',
  headers: {
    'Authorization': bearerToken,
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({ sql, params })
})
```

### Component Structure
- `src/components/ui/` - shadcn/ui base components
- `src/components/layout/` - Admin sidebar with navigation and theme toggle
- `src/components/tables/` - Data tables with pagination and filtering
- `src/components/charts/` - Recharts analytics visualizations
- `src/components/forms/` - Form components for user/model management
- `src/components/providers/` - Context providers (Auth, Theme, Toast)

### State Management & Caching
- React Context for authentication (`AuthProvider`) and theme (`ThemeProvider`)
- Local component state with hooks
- Server-side caching via `ServerCache` from `@/lib/cache`
- No global state management library

#### **Server-Side Caching Strategy**
```typescript
// Cache implementation using ServerCache
import { serverCache } from '@/lib/cache'

// Cache data for 5 minutes (normal) or 2 minutes (empty results)
serverCache.set(cacheKey, data, 5) // 5 minute TTL
serverCache.set(cacheKey, emptyData, 2) // 2 minute TTL for empty

// Cache bypass for refresh functionality
if (forceRefresh) {
  serverCache.delete(cacheKey) // Delete specific cache entry
}
```

#### **Caching by Endpoint**
- **Analytics** (`/api/analytics`): ✅ Cached - 5min data, 2min empty
- **Logs** (`/api/logs`): ✅ Cached - 5min data, 2min empty
- **Bucket** (`/api/bucket/list`): ✅ Cached - 5min data, 2min empty
- **Models** (`/api/models`): ❌ No caching - Real-time CRUD operations
- **Users** (`/api/users`): ❌ No caching - Real-time CRUD operations
- **Bucket Operations** (`/api/bucket/delete`, `/api/bucket/download`, `/api/bucket/preview`): ❌ No caching - Real-time operations
- **SQL Editor** (`/api/sql-execute`): ❌ No caching - Real-time SQL execution

### UI System
- Tailwind CSS v4 with CSS variables for theming
- shadcn/ui component system with custom theme variables
- Dark/light mode via `data-theme` attribute and CSS custom properties
- Toast notifications via Sonner

## Key Files

### Configuration
- `next.config.ts` - ESLint/TypeScript errors ignored for builds
- `.env.local` - Environment variables (Supabase keys + N8N webhook URLs)
- `src/types/index.ts` - Centralized TypeScript definitions

### Critical Utilities
- `src/lib/supabase.ts` - Authentication client factories
- `src/lib/jwt.ts` - JWT token generation for webhook authentication
- `src/lib/cache.ts` - Server-side caching with `ServerCache` class
- `src/lib/webhooks.ts` - Legacy webhook utilities (being phased out)
- `src/middleware.ts` - Global auth protection

### Main Features
- `/analytics` - Dashboard with metrics cards and Recharts visualizations (✅ cached)
- `/models` - LLM model CRUD with pricing configuration (⚡ real-time)
- `/users` - User creation and problem resolution actions (⚡ real-time)
- `/logs` - Paginated request logs with advanced filtering (✅ cached)
- `/bucket` - Cloudflare R2 file management with preview, download, delete (✅ cached listings)
  - Hierarchical folder navigation (photos/audio → user-id → files)
  - Image preview with full-screen gallery view
  - HTML5 audio player for .m4a files
  - Presigned URLs for secure file operations
  - Server-side caching for directory listings with refresh bypass
- `/sql-editor` - PostgreSQL query execution interface with comment filtering (⚡ real-time)
  - Direct SQL execution with JWT-authenticated webhook calls
  - Smart comment filtering (removes `--` lines and inline comments)
  - JSON-only results display with configurable pagination (10/20/50/100 rows)
  - Keyboard shortcuts: Cmd+Enter (execute), Cmd+L (comment/uncomment)
  - Copy/download full results functionality with execution time tracking

#### **Feature Implementation Status**
All features use modern direct patterns (JWT + webhook or R2 direct):
- ✅ **Analytics**: Migrated with caching + refresh bypass
- ✅ **Logs**: Migrated with caching + refresh bypass  
- ✅ **Models**: Already using direct pattern (no caching needed)
- ✅ **Users**: Already using direct pattern (no caching needed)
- ✅ **Bucket**: Implemented with R2 integration and caching for listings
- ✅ **SQL Editor**: Implemented with comment filtering and pagination (no caching needed)

## Development Patterns

### Adding New Features
1. Create page directory and `page.tsx` in `app/` folder
2. Add API routes in `api/` folder using the modern direct webhook pattern
3. Decide if caching is needed (read-heavy vs real-time operations)
4. Create reusable components in `components/`
5. Update types in `src/types/index.ts`

#### **API Route Template (Modern Pattern)**
```typescript
import { NextRequest, NextResponse } from 'next/server'
import { createBearerToken } from '@/lib/jwt'
import { serverCache } from '@/lib/cache' // Only if caching needed

export async function GET(request: NextRequest) {
  try {
    // Environment validation
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    if (!webhookUrl) {
      return NextResponse.json(
        { success: false, error: 'Database webhook configuration is missing' },
        { status: 500 }
      )
    }

    // Cache logic (optional - for read-heavy operations only)
    const cacheKey = 'unique_cache_key'
    const forceRefresh = request.nextUrl.searchParams.get('refresh') === 'true'
    
    if (forceRefresh) {
      serverCache.delete(cacheKey)
    }
    
    const cachedData = serverCache.get(cacheKey)
    if (cachedData) {
      return NextResponse.json({ success: true, data: cachedData, cached: true })
    }

    // Direct webhook call
    const bearerToken = createBearerToken()
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Authorization': bearerToken,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ sql: 'SELECT * FROM table WHERE id = $1', params: [id] })
    })

    // Response handling
    const responseText = await response.text()
    if (!responseText?.trim()) {
      return NextResponse.json({ success: true, data: [] })
    }

    const result = JSON.parse(responseText)
    if (!response.ok) {
      return NextResponse.json(
        { success: false, error: result.error_msg || 'Database query failed' },
        { status: response.status }
      )
    }

    // Extract data (webhook response format)
    let data = result.body || result
    if (result.body && Array.isArray(result.body)) {
      data = result.body
    } else if (Array.isArray(result)) {
      data = result
    }

    // Cache result (optional)
    serverCache.set(cacheKey, data, 5)

    return NextResponse.json({ success: true, data })
  } catch (error) {
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
```

#### **R2 API Route Template**
```typescript
import { NextRequest, NextResponse } from 'next/server'
import { S3Client, ListObjectsV2Command } from '@aws-sdk/client-s3'
import { serverCache } from '@/lib/cache'

// Initialize S3 client for Cloudflare R2
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
  },
})

export async function GET(request: NextRequest) {
  try {
    // Environment validation
    if (!process.env.CLOUDFLARE_R2_ENDPOINT || 
        !process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || 
        !process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY ||
        !process.env.CLOUDFLARE_R2_BUCKET_NAME) {
      return NextResponse.json(
        { success: false, error: 'Cloudflare R2 configuration is missing' },
        { status: 500 }
      )
    }

    // Cache logic (optional - for read-heavy operations only)
    const cacheKey = 'unique_r2_cache_key'
    const forceRefresh = request.nextUrl.searchParams.get('refresh') === 'true'
    
    if (forceRefresh) {
      serverCache.delete(cacheKey)
    }
    
    const cachedData = serverCache.get(cacheKey)
    if (cachedData) {
      return NextResponse.json({ success: true, data: cachedData, cached: true })
    }

    // R2 operations
    const command = new ListObjectsV2Command({
      Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
      // Add specific R2 operation parameters here
    })

    const response = await s3Client.send(command)
    
    // Process R2 response
    const data = response.Contents || []
    
    // Cache result (optional)
    serverCache.set(cacheKey, data, 5)

    return NextResponse.json({ success: true, data })
  } catch (error) {
    console.error('R2 operation error:', error)
    return NextResponse.json(
      { success: false, error: 'R2 operation failed' },
      { status: 500 }
    )
  }
}
```

### Database Operations
All database operations use raw SQL sent directly to N8N webhooks with JWT authentication:

#### **Direct Webhook Pattern (Modern)**
```typescript
import { createBearerToken } from '@/lib/jwt'

const bearerToken = createBearerToken()
const response = await fetch(process.env.CHHLAT_DB_WEBHOOK_URL, {
  method: 'POST',
  headers: {
    'Authorization': bearerToken,
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({ sql, params })
})
```

#### **Implementation Guidelines**
- Write parameterized SQL with `$1, $2, $3` placeholders for security
- Always validate environment variables (`CHHLAT_DB_WEBHOOK_URL`, `CHHLAT_DB_WEBHOOK_TOKEN`)
- Handle webhook response parsing and error cases
- Use caching for read-heavy operations (analytics, logs)
- Skip caching for real-time CRUD operations (models, users)
- Never use Supabase client for database operations (auth only)

### Webhook Integration
All external data must go through server-side API routes that call N8N webhooks:

#### **Modern Implementation Pattern**
- Never call webhook URLs directly from client components
- Use direct JWT-authenticated webhook calls in API routes
- Implement caching for read-heavy endpoints (analytics, logs)
- Handle loading states and error cases properly

#### **Cache Implementation (for analytics/logs)**
```typescript
import { serverCache } from '@/lib/cache'

// Check for refresh bypass
const forceRefresh = searchParams.get('refresh') === 'true'
if (forceRefresh) {
  serverCache.delete(cacheKey)
}

// Check cache
const cachedData = serverCache.get(cacheKey)
if (cachedData) {
  return NextResponse.json({ success: true, data: cachedData, cached: true })
}

// Fetch from webhook and cache result
const data = await fetchFromWebhook()
serverCache.set(cacheKey, data, 5) // 5 minute cache
```

#### **Legacy Pattern (Deprecated)**
- `src/lib/webhooks.ts` utility functions are being phased out
- Use direct webhook calls instead of `analyticsWebhooks.*` functions

### Cloudflare R2 Integration Pattern
File storage operations using AWS S3-compatible API with Cloudflare R2:

#### **R2 Direct Pattern**
Used by: `/api/bucket/list`, `/api/bucket/delete`, `/api/bucket/download`, `/api/bucket/preview`
- Direct AWS S3 SDK calls to Cloudflare R2 endpoints
- Presigned URLs for secure file access and downloads
- Server-side caching for directory listings (5min TTL, 2min for empty)
- No webhook integration needed - direct R2 API calls
- Automatic cache invalidation on file operations

#### **Implementation Example**
```typescript
// R2 S3 client setup
import { S3Client, ListObjectsV2Command } from '@aws-sdk/client-s3'

const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
  },
})

// Directory listing with caching
const command = new ListObjectsV2Command({
  Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
  Prefix: path ? `${path}/` : '',
  Delimiter: '/',
})
const response = await s3Client.send(command)
```

### Authentication
- Server components: Use `createServerSupabaseClient()`
- Client components: Use `createClient()` with `useAuth()` hook
- API routes: Use appropriate client based on context
- All auth state managed by `AuthProvider` context

### Styling
- Use existing shadcn/ui components where possible
- Follow Tailwind CSS v4 patterns with CSS variables
- Maintain theme consistency across light/dark modes
- Use design tokens from globals.css

## Environment Variables Required

```bash
# Supabase
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# N8N Webhooks (all server-side only)
CHHLAT_DB_WEBHOOK_URL=        # Database operations with raw SQL
CHHLAT_DB_WEBHOOK_TOKEN=      # Database webhook authentication

# Cloudflare R2 (file storage)
CLOUDFLARE_R2_ENDPOINT=           # R2 storage endpoint URL
CLOUDFLARE_R2_ACCESS_KEY_ID=      # R2 access key ID  
CLOUDFLARE_R2_SECRET_ACCESS_KEY=  # R2 secret access key
CLOUDFLARE_R2_BUCKET_NAME=        # R2 bucket name (currently: "file")
CLOUDFLARE_R2_PUBLIC_URL=         # Public URL for accessing stored files (optional)
```

## Security Notes

- All webhook calls must be server-side only (API routes)
- Never expose webhook URLs to client-side code
- Validate all inputs before sending to webhooks
- Use service role key only for admin operations
- Middleware provides automatic auth protection
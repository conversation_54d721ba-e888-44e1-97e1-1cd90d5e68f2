import { redirect } from 'next/navigation'
import { verifyAdminAuth } from '@/utils/auth'

export default async function HomePage() {
  // Check if user is authenticated and admin
  const { authenticated, isAdmin } = await verifyAdminAuth()
  
  if (authenticated && isAdmin) {
    // Redirect authenticated admin users to users page
    redirect('/users')
  } else {
    // Redirect non-authenticated users to login
    redirect('/login')
  }
}
'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/providers/auth-provider'

export default function HomePage() {
  const router = useRouter()
  const { user, loading } = useAuth()

  useEffect(() => {
    if (loading) return // Wait for auth to load

    if (user) {
      // Check if user has admin role
      const userRole = user.app_metadata?.role
      if (userRole === 'admin') {
        // Redirect authenticated admin users to users page
        router.push('/users')
      } else {
        // Redirect non-admin users to login (they shouldn't be here)
        router.push('/login')
      }
    } else {
      // Redirect non-authenticated users to login
      router.push('/login')
    }
  }, [user, loading, router])

  // Show loading state while determining redirect
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
        <p className="mt-2 text-sm text-muted-foreground">Loading...</p>
      </div>
    </div>
  )
}
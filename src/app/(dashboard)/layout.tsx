import { redirect } from 'next/navigation'
import { verifyAdminAuth } from '@/utils/auth'

// Force dynamic rendering for all dashboard routes since they require authentication
export const dynamic = 'force-dynamic'

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Verify admin authentication for all dashboard routes
  const { authenticated, isAdmin } = await verifyAdminAuth()

  if (!authenticated || !isAdmin) {
    // Redirect non-authenticated or non-admin users to login
    redirect('/login')
  }

  return <>{children}</>
}
'use client'

import { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { DatePickerWithRange } from '@/components/ui/date-range-picker'
import { AnalyticsCharts } from '@/components/charts/analytics-charts'
import { MetricsCards } from '@/components/charts/metrics-cards'
import { ChartPeriodFilter } from '@/components/charts/chart-period-filter'
import { DetailedAnalysisSection } from '@/components/charts/detailed-analysis'
import { toast } from 'sonner'
import { AnalyticsOverview, DetailedAnalytics, ChartPeriod } from '@/types'
import { RefreshCw } from 'lucide-react'
import { addDays, format, differenceInDays, subDays } from 'date-fns'
import { DateRange } from 'react-day-picker'

export default function AnalyticsPage() {
  const [analytics, setAnalytics] = useState<AnalyticsOverview | null>(null)
  const [loading, setLoading] = useState(false)
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: addDays(new Date(), -30),
    to: new Date(),
  })
  const [chartPeriod, setChartPeriod] = useState<ChartPeriod>({ label: '1 Week', days: 7 })

  const fetchAnalytics = async (forceRefresh: boolean = false) => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      if (dateRange?.from) {
        params.append('start_date', format(dateRange.from, 'yyyy-MM-dd'))
      }
      if (dateRange?.to) {
        params.append('end_date', format(addDays(dateRange.to, 1), 'yyyy-MM-dd'))
      }

      // Add refresh parameter when refresh button is clicked
      if (forceRefresh) {
        params.append('refresh', 'true')
      }

      const response = await fetch(`/api/analytics?${params}`)
      const result = await response.json()

      if (result.success) {
        setAnalytics(result.data)
      } else {
        toast.error(result.error || 'Failed to fetch analytics')
      }
    } catch {
      toast.error('An error occurred while fetching analytics')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAnalytics()
  }, [dateRange])

  // Calculate available days and filtered chart data
  const { availableDays, filteredChartData } = useMemo(() => {
    const days = dateRange?.from && dateRange?.to 
      ? differenceInDays(dateRange.to, dateRange.from) + 1 
      : 0

    // Filter chart data based on selected period
    let filteredRequests = analytics?.daily_requests || []
    let filteredCosts = analytics?.daily_costs || []

    if (analytics && chartPeriod.days < days) {
      const cutoffDate = dateRange?.to ? subDays(dateRange.to, chartPeriod.days - 1) : new Date()
      const cutoffString = format(cutoffDate, 'yyyy-MM-dd')
      
      filteredRequests = analytics.daily_requests?.filter(item => item.date >= cutoffString) || []
      filteredCosts = analytics.daily_costs?.filter(item => item.date >= cutoffString) || []
    }

    return {
      availableDays: days,
      filteredChartData: {
        requests: filteredRequests,
        costs: filteredCosts,
      }
    }
  }, [analytics, dateRange, chartPeriod])

  // Generate detailed analytics from the single analytics response
  const detailedAnalytics = useMemo(() => {
    if (!analytics?.recent_logs) return null

    const aggregations = {
      operations: new Map<string, { requests: number; tokens: number; cost: number }>(),
      platforms: new Map<string, { requests: number; tokens: number; cost: number }>(),
      clients: new Map<string, { requests: number; tokens: number; cost: number }>(),
      models: new Map<string, { requests: number; tokens: number; cost: number }>(),
    }

    analytics.recent_logs.forEach(log => {
      // Skip logs with missing required fields
      if (!log.operation || !log.platform || !log.client_id || !log.model) {
        return
      }

      // Convert string values to numbers and handle NaN
      const logCost = Number(log.total_cost) || 0
      const inputTokens = Number(log.input_tokens) || 0
      const outputTokens = Number(log.output_tokens) || 0
      const cacheReadTokens = Number(log.cache_read_tokens) || 0
      const cacheWriteTokens = Number(log.cache_write_tokens) || 0
      const thinkingTokens = Number(log.thinking_tokens) || 0
      const audioInputTokens = Number(log.audio_input_tokens) || 0

      // Exclude cache read tokens as they're already counted in input tokens
      const totalTokens = inputTokens + outputTokens + 
                         cacheWriteTokens + thinkingTokens + audioInputTokens

      // Aggregate by operation (with null check)
      const operation = log.operation || 'Unknown'
      const opData = aggregations.operations.get(operation) || { requests: 0, tokens: 0, cost: 0 }
      opData.requests += 1
      opData.tokens += totalTokens
      opData.cost += logCost
      aggregations.operations.set(operation, opData)

      // Aggregate by platform (with null check)
      const platform = log.platform || 'Unknown'
      const platformData = aggregations.platforms.get(platform) || { requests: 0, tokens: 0, cost: 0 }
      platformData.requests += 1
      platformData.tokens += totalTokens
      platformData.cost += logCost
      aggregations.platforms.set(platform, platformData)

      // Aggregate by client (with null check)
      const clientId = log.client_id || 'Unknown'
      const clientData = aggregations.clients.get(clientId) || { requests: 0, tokens: 0, cost: 0 }
      clientData.requests += 1
      clientData.tokens += totalTokens
      clientData.cost += logCost
      aggregations.clients.set(clientId, clientData)

      // Aggregate by model (with null check)
      const model = log.model || 'Unknown'
      const modelData = aggregations.models.get(model) || { requests: 0, tokens: 0, cost: 0 }
      modelData.requests += 1
      modelData.tokens += totalTokens
      modelData.cost += logCost
      aggregations.models.set(model, modelData)
    })

    // Convert maps to arrays and sort by cost (descending)
    return {
      operations: Array.from(aggregations.operations.entries())
        .map(([name, data]) => ({ name, ...data }))
        .sort((a, b) => b.cost - a.cost),
      platforms: Array.from(aggregations.platforms.entries())
        .map(([name, data]) => ({ name, ...data }))
        .sort((a, b) => b.cost - a.cost),
      clients: Array.from(aggregations.clients.entries())
        .map(([name, data]) => ({ name, ...data }))
        .sort((a, b) => b.cost - a.cost),
      models: Array.from(aggregations.models.entries())
        .map(([name, data]) => ({ name, ...data }))
        .sort((a, b) => b.cost - a.cost),
    }
  }, [analytics])

  const handleRefresh = () => {
    fetchAnalytics(true)
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor LLM usage, costs, and performance metrics
          </p>
        </div>
        <div className="flex gap-2">
          <DatePickerWithRange
            date={dateRange}
            onDateChange={setDateRange}
          />
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      <MetricsCards analytics={analytics} loading={loading} />

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div>
              <CardTitle>Daily Requests</CardTitle>
              <CardDescription>
                Number of API requests per day
              </CardDescription>
            </div>
            <ChartPeriodFilter
              selectedPeriod={chartPeriod}
              onPeriodChange={setChartPeriod}
              maxDays={availableDays}
            />
          </CardHeader>
          <CardContent>
            <AnalyticsCharts
              data={filteredChartData.requests}
              type="requests"
              loading={loading}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div>
              <CardTitle>Daily Cost Spend</CardTitle>
              <CardDescription>
                Total cost breakdown per day
              </CardDescription>
            </div>
            <ChartPeriodFilter
              selectedPeriod={chartPeriod}
              onPeriodChange={setChartPeriod}
              maxDays={availableDays}
            />
          </CardHeader>
          <CardContent>
            <AnalyticsCharts
              data={filteredChartData.costs}
              type="costs"
              loading={loading}
            />
          </CardContent>
        </Card>
      </div>

      <DetailedAnalysisSection 
        data={detailedAnalytics} 
        loading={loading} 
      />
    </div>
  )
}
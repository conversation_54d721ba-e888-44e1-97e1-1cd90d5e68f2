'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { DatabaseTableViewer } from '@/components/tables/database-table-viewer'
import { DATABASE_TABLES } from '@/lib/database'
import { Database, Info } from 'lucide-react'

export default function TablesPage() {
  const [activeTab, setActiveTab] = useState<string | null>(null)

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <Database className="h-6 w-6" />
          <h1 className="text-3xl font-bold">Database Tables</h1>
        </div>
        <Badge variant="outline" className="text-sm">
          {DATABASE_TABLES.length} Tables
        </Badge>
      </div>

      <div className="text-muted-foreground">
        <p>View and explore all database tables in the system. Each tab represents a different table with its schema and data.</p>
      </div>

      <Tabs value={activeTab || ''} onValueChange={setActiveTab} className="w-full">
        <div className="overflow-x-auto">
          <TabsList className="flex w-fit h-auto p-1 gap-1">
            {DATABASE_TABLES.map((table) => (
              <TabsTrigger
                key={table.name}
                value={table.name}
                className="flex flex-col items-center gap-1 h-16 px-3 text-xs whitespace-nowrap"
              >
                <Database className="h-4 w-4" />
                <span className="font-medium">{table.displayName}</span>
              </TabsTrigger>
            ))}
          </TabsList>
        </div>

        {!activeTab && (
          <div className="text-center py-12 text-muted-foreground">
            <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium">Select a table to view its contents</p>
            <p className="text-sm mt-2">Choose from the {DATABASE_TABLES.length} available tables above</p>
          </div>
        )}

        {DATABASE_TABLES.map((table) => (
          <TabsContent key={table.name} value={table.name} className="space-y-6">
            <DatabaseTableViewer
              tableName={table.name}
              displayName={table.displayName}
              shouldLoad={activeTab === table.name}
            />
          </TabsContent>
        ))}
      </Tabs>
    </div>
  )
}
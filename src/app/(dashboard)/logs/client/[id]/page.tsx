'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { DetailedLogsTable } from '@/components/tables/detailed-logs-table'
import { toast } from 'sonner'
import { RequestLogData } from '@/types'
import { ArrowLeft, RefreshCw } from 'lucide-react'

export default function ClientDetailPage() {
  const params = useParams()
  const router = useRouter()
  const clientId = params.id as string
  
  const [logs, setLogs] = useState<RequestLogData[]>([])
  const [loading, setLoading] = useState(false)
  const [pagination, setPagination] = useState({
    page: 1,
    rows: 50,
    total: 0,
    total_pages: 0,
  })

  const fetchClientLogs = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        mode: 'log-filter',
        type: 'client',
        filter_data: clientId,
        page: pagination.page.toString(),
        rows: pagination.rows.toString(),
      })

      const response = await fetch(`/api/logs?${params}`)
      const result = await response.json()

      if (result.success) {
        const data = result.data
        setLogs(data.data || [])
        setPagination(prev => ({
          ...prev,
          total: data.total,
          total_pages: data.total_pages,
        }))
      } else {
        toast.error(result.error || 'Failed to fetch client logs')
      }
    } catch (error) {
      toast.error('An error occurred while fetching client logs')
    } finally {
      setLoading(false)
    }
  }

  const handleRowsChange = (newRows: number) => {
    setPagination(prev => ({ ...prev, rows: newRows, page: 1 }))
  }

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }))
  }

  useEffect(() => {
    fetchClientLogs()
  }, [clientId, pagination.page, pagination.rows])

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => router.push('/logs')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Logs
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Client Details</h1>
            <p className="text-muted-foreground font-mono">
              {clientId}
            </p>
          </div>
        </div>
        <Button
          variant="outline"
          onClick={fetchClientLogs}
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Client Logs</span>
            <div className="flex items-center gap-2">
              <Label htmlFor="rows">Rows:</Label>
              <Select
                value={pagination.rows.toString()}
                onValueChange={(value) => handleRowsChange(parseInt(value))}
              >
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                  <SelectItem value="200">200</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardTitle>
          <CardDescription>
            {pagination.total > 0
              ? `Showing ${(pagination.page - 1) * pagination.rows + 1}-${Math.min(
                  pagination.page * pagination.rows,
                  pagination.total
                )} of ${pagination.total} logs`
              : 'No logs found for this client'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DetailedLogsTable
            logs={logs}
            loading={loading}
            pagination={{
              page: pagination.page,
              limit: pagination.rows,
              total: pagination.total,
              onPageChange: handlePageChange,
              onLimitChange: handleRowsChange,
            }}
          />
        </CardContent>
      </Card>
    </div>
  )
}
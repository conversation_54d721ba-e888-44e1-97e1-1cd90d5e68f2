'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { RequestLogsTable } from '@/components/tables/request-logs-table'
import { DatePickerWithRange } from '@/components/ui/date-range-picker'
import { toast } from 'sonner'
import { RequestLogData, PaginatedResponse } from '@/types'
import { RefreshCw, Calendar, ArrowLeft } from 'lucide-react'
import { addDays } from 'date-fns'
import { DateRange } from 'react-day-picker'

export default function LogsPage() {
  // Request Logs tab state
  const [requestLogs, setRequestLogs] = useState<RequestLogData[]>([])
  const [requestLoading, setRequestLoading] = useState(false)
  const [requestPagination, setRequestPagination] = useState({
    page: 1,
    rows: 50,
    total: 0,
    total_pages: 0,
  })
  
  const [requestFilters, setRequestFilters] = useState({
    conversation_id: '',
    client_id: '',
  })
  
  const [requestDateRange, setRequestDateRange] = useState<DateRange | undefined>({
    from: addDays(new Date(), -7),
    to: new Date(),
  })

  // Filters tab state
  const [filterLogs, setFilterLogs] = useState<RequestLogData[]>([])
  const [filterLoading, setFilterLoading] = useState(false)
  const [filterPagination, setFilterPagination] = useState({
    page: 1,
    rows: 50,
    total: 0,
    total_pages: 0,
  })
  
  const [filterFilters, setFilterFilters] = useState({
    task: '',
  })

  const [activeTab, setActiveTab] = useState('request-logs')
  const [filterForm, setFilterForm] = useState({
    task: '',
    rows: 50,
  })
  const [hasAppliedFilter, setHasAppliedFilter] = useState(false)
  const [showFilteredTable, setShowFilteredTable] = useState(false)
  const [filteredLogs, setFilteredLogs] = useState<RequestLogData[]>([])
  const [filteredLoading, setFilteredLoading] = useState(false)

  const fetchRequestLogs = async (forceRefresh: boolean = false) => {
    setRequestLoading(true)
    try {
      const params = new URLSearchParams({
        page: requestPagination.page.toString(),
        rows: requestPagination.rows.toString(),
      })

      // Add date range (no filters for original table)
      if (requestDateRange?.from) {
        params.append('start_date', requestDateRange.from.toISOString().split('T')[0])
      }
      if (requestDateRange?.to) {
        params.append('end_date', addDays(requestDateRange.to, 1).toISOString().split('T')[0])
      }

      // Add refresh parameter when refresh button is clicked
      if (forceRefresh) {
        params.append('refresh', 'true')
      }

      const response = await fetch(`/api/logs?${params}`)
      const result = await response.json()

      if (result.success) {
        const data = result.data
        setRequestLogs(data.data || [])
        setRequestPagination({
          page: data.page,
          rows: requestPagination.rows,
          total: data.total,
          total_pages: data.total_pages,
        })
      } else {
        toast.error(result.error || 'Failed to fetch request logs')
      }
    } catch (error) {
      toast.error('An error occurred while fetching request logs')
    } finally {
      setRequestLoading(false)
    }
  }

  const fetchFilteredLogs = async (filterType: string, filterValue: string) => {
    setFilteredLoading(true)
    try {
      const params = new URLSearchParams({
        page: '1',
        rows: '50',
        [filterType]: filterValue,
      })

      // Add date range
      if (requestDateRange?.from) {
        params.append('start_date', requestDateRange.from.toISOString().split('T')[0])
      }
      if (requestDateRange?.to) {
        params.append('end_date', addDays(requestDateRange.to, 1).toISOString().split('T')[0])
      }

      const response = await fetch(`/api/logs?${params}`)
      const result = await response.json()

      if (result.success) {
        const data = result.data
        setFilteredLogs(data.data || [])
        setShowFilteredTable(true)
      } else {
        toast.error(result.error || 'Failed to fetch filtered logs')
      }
    } catch (error) {
      toast.error('An error occurred while fetching filtered logs')
    } finally {
      setFilteredLoading(false)
    }
  }

  const fetchFilterLogs = async (forceRefresh: boolean = false) => {
    setFilterLoading(true)
    try {
      const params = new URLSearchParams({
        page: filterPagination.page.toString(),
        rows: filterPagination.rows.toString(),
      })

      // Add task filter
      if (filterFilters.task) {
        params.append('task_value', filterFilters.task)
      }

      // Add date range (same as request logs tab)
      if (requestDateRange?.from) {
        params.append('start_date', requestDateRange.from.toISOString().split('T')[0])
      }
      if (requestDateRange?.to) {
        params.append('end_date', addDays(requestDateRange.to, 1).toISOString().split('T')[0])
      }

      // Add refresh parameter when refresh button is clicked
      if (forceRefresh) {
        params.append('refresh', 'true')
      }

      const response = await fetch(`/api/logs?${params}`)
      const result = await response.json()

      if (result.success) {
        const data = result.data
        setFilterLogs(data.data || [])
        setFilterPagination({
          page: data.page,
          rows: filterPagination.rows,
          total: data.total,
          total_pages: data.total_pages,
        })
      } else {
        toast.error(result.error || 'Failed to fetch filtered logs')
      }
    } catch (error) {
      toast.error('An error occurred while fetching filtered logs')
    } finally {
      setFilterLoading(false)
    }
  }

  // Request Logs tab useEffect hooks
  useEffect(() => {
    if (!showFilteredTable) {
      fetchRequestLogs()
    }
  }, [requestPagination.page, requestPagination.rows, requestDateRange, showFilteredTable])

  // Filters tab useEffect hooks
  useEffect(() => {
    if (filterFilters.task) {
      fetchFilterLogs()
    }
  }, [filterFilters, filterPagination.page, filterPagination.rows, requestDateRange])

  // Request Logs tab handlers
  const handleConversationFilter = (conversationId: string) => {
    fetchFilteredLogs('conversation_id', conversationId)
  }

  const handleClientFilter = (clientId: string) => {
    fetchFilteredLogs('client_id', clientId)
  }

  const handleBackToRequestLogs = () => {
    setShowFilteredTable(false)
    setFilteredLogs([])
    setRequestFilters({
      conversation_id: '',
      client_id: '',
    })
  }


  const handleRequestPageChange = (newPage: number) => {
    setRequestPagination(prev => ({ ...prev, page: newPage }))
  }

  const handleRequestRowsChange = (newRows: number) => {
    setRequestPagination(prev => ({ ...prev, rows: newRows, page: 1 }))
  }

  // Filters tab handlers
  const handleTaskFilterChange = (task: string) => {
    setFilterForm(prev => ({ ...prev, task }))
  }

  const handleFilterFormRowsChange = (rows: number) => {
    setFilterForm(prev => ({ ...prev, rows }))
  }

  const handleApplyTaskFilter = () => {
    if (filterForm.task) {
      setFilterFilters({ task: filterForm.task })
      setFilterPagination(prev => ({ ...prev, rows: filterForm.rows, page: 1 }))
      setHasAppliedFilter(true)
    }
  }

  const handleClearTaskFilters = () => {
    setFilterFilters({ task: '' })
    setFilterForm({ task: '', rows: 50 })
    setHasAppliedFilter(false)
    setFilterPagination(prev => ({ ...prev, page: 1 }))
  }

  const handleFilterPageChange = (newPage: number) => {
    setFilterPagination(prev => ({ ...prev, page: newPage }))
  }

  const handleFilterRowsChange = (newRows: number) => {
    setFilterPagination(prev => ({ ...prev, rows: newRows, page: 1 }))
  }

  const taskOptions = [
    'combine_input', 'normalize', 'check_answer', 'gen_open_close_response_text', 'gen_close_response_audio',
    'gen_clarification_response', 'gen_escalation_response', 'combine_input_purcahse_action', 'translate_english_khmer',
    'classify_casual', 'casual_conversation', 'classify_action', 'initial_purchase_response', 'process_collect_item',
    'process_collect_item_g', 'process_collect_contact', 'process_collect_contact_g', 'process_collect_phone_only',
    'process_collect_phone_only_g', 'handle_delivery_inquiry', 'extract_delivery_info', 'handle_purchase_issue',
    'extract_purchase_issue_info', 'transcribe_audio', 'embed', 'faq_normalize_english', 'faq_normalize_khmer',
    'faq_normalize_g', 'faq_embed', 'faq_transcribe_english_audio', 'faq_transcribe_khmer_audio',
    'faq_transcribe_g_audio', 'faq_clean_english_transcription', 'faq_clean_english_answer',
    'faq_translate_khmer_transcription', 'faq_translate_khmer_answer', 'faq_translate_g_transcription',
    'faq_translate_g_answer', 'faq_generate_variations'
  ]

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Request Logs</h1>
          <p className="text-muted-foreground">
            View detailed request logs from your LLM applications
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => activeTab === 'request-logs' ? fetchRequestLogs(true) : fetchFilterLogs(true)}
          disabled={activeTab === 'request-logs' ? requestLoading : filterLoading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${(activeTab === 'request-logs' ? requestLoading : filterLoading) ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="request-logs">Request Logs</TabsTrigger>
          <TabsTrigger value="filters">Filters</TabsTrigger>
        </TabsList>

        <TabsContent value="request-logs" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Date Range
              </CardTitle>
              <CardDescription>
                Select date range to view logs
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <DatePickerWithRange
                  date={requestDateRange}
                  onDateChange={setRequestDateRange}
                  className="w-auto"
                />
                <div className="flex items-center gap-2">
                  <Label htmlFor="rows">Rows:</Label>
                  <Select
                    value={requestPagination.rows.toString()}
                    onValueChange={(value) => handleRequestRowsChange(parseInt(value))}
                  >
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                      <SelectItem value="200">200</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {!showFilteredTable ? (
            <Card>
              <CardHeader>
                <CardTitle>Request Logs</CardTitle>
                <CardDescription>
                  {requestPagination.total > 0
                    ? `Showing ${(requestPagination.page - 1) * requestPagination.rows + 1}-${Math.min(
                        requestPagination.page * requestPagination.rows,
                        requestPagination.total
                      )} of ${requestPagination.total} logs`
                    : 'No logs found'
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                <RequestLogsTable
                  logs={requestLogs}
                  loading={requestLoading}
                  pagination={{
                    page: requestPagination.page,
                    limit: requestPagination.rows,
                    total: requestPagination.total,
                    onPageChange: handleRequestPageChange,
                    onLimitChange: handleRequestRowsChange,
                  }}
                  onConversationFilter={handleConversationFilter}
                  onClientFilter={handleClientFilter}
                />
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <div className="flex items-center gap-4">
                  <Button
                    variant="outline"
                    onClick={handleBackToRequestLogs}
                    className="flex items-center gap-2"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    Back
                  </Button>
                  <div>
                    <CardTitle>Filtered Results</CardTitle>
                    <CardDescription>
                      {filteredLogs.length > 0
                        ? `Showing ${filteredLogs.length} filtered logs`
                        : 'No filtered logs found'
                      }
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <RequestLogsTable
                  logs={filteredLogs}
                  loading={filteredLoading}
                  pagination={{
                    page: 1,
                    limit: 50,
                    total: filteredLogs.length,
                    onPageChange: () => {},
                    onLimitChange: () => {},
                  }}
                />
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="filters" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Date Range
              </CardTitle>
              <CardDescription>
                Select date range for filtering (applies to both task and other filters)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <DatePickerWithRange
                  date={requestDateRange}
                  onDateChange={setRequestDateRange}
                  className="w-auto"
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Task Filters</CardTitle>
              <CardDescription>
                Filter logs by specific task types
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Label htmlFor="task">Task:</Label>
                  <Select
                    value={filterForm.task}
                    onValueChange={handleTaskFilterChange}
                  >
                    <SelectTrigger className="w-64">
                      <SelectValue placeholder="Select a task" />
                    </SelectTrigger>
                    <SelectContent>
                      {taskOptions.map((task) => (
                        <SelectItem key={task} value={task}>
                          {task}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center gap-2">
                  <Label htmlFor="rows">Rows:</Label>
                  <Select
                    value={filterForm.rows.toString()}
                    onValueChange={(value) => handleFilterFormRowsChange(parseInt(value))}
                  >
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                      <SelectItem value="200">200</SelectItem>
                      <SelectItem value="500">500</SelectItem>
                      <SelectItem value="1000">1000</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button onClick={handleApplyTaskFilter} disabled={filterLoading || !filterForm.task}>
                  Filter
                </Button>
                {hasAppliedFilter && (
                  <Button variant="outline" onClick={handleClearTaskFilters}>
                    Clear Filters
                  </Button>
                )}
              </div>
              {filterForm.task && (
                <div className="text-sm text-muted-foreground">
                  Selected task: <span className="font-mono">{filterForm.task}</span> (click Filter to apply)
                </div>
              )}
            </CardContent>
          </Card>

          {/* Display filtered results */}
          {hasAppliedFilter && filterFilters.task && (
            <Card>
              <CardHeader>
                <CardTitle>Filtered Results</CardTitle>
                <CardDescription>
                  {filterPagination.total > 0
                    ? `Showing ${(filterPagination.page - 1) * filterPagination.rows + 1}-${Math.min(
                        filterPagination.page * filterPagination.rows,
                        filterPagination.total
                      )} of ${filterPagination.total} logs for task: ${filterFilters.task}`
                    : `No logs found for task: ${filterFilters.task}`
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                <RequestLogsTable
                  logs={filterLogs}
                  loading={filterLoading}
                  pagination={{
                    page: filterPagination.page,
                    limit: filterPagination.rows,
                    total: filterPagination.total,
                    onPageChange: handleFilterPageChange,
                    onLimitChange: handleFilterRowsChange,
                  }}
                />
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
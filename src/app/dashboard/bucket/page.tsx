'use client'

import { useState, useEffect } from 'react'
import { RefreshCw, Home, ChevronRight, Folder, File, Image, Music, Download, Trash2, ExternalLink, Copy } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog'
import { toast } from 'sonner'
import { BucketFile, BucketListResponse, BucketDownloadResponse, BucketPreviewResponse } from '@/types'

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function formatDate(date: Date): string {
  return new Date(date).toLocaleDateString() + ' ' + new Date(date).toLocaleTimeString()
}

function getPublicUrl(fileKey: string): string {
  const publicBaseUrl = process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL
  if (!publicBaseUrl) {
    return `https://your-r2-domain.com/${fileKey}`
  }
  return `${publicBaseUrl}/${fileKey}`
}

export default function BucketPage() {
  const [currentPath, setCurrentPath] = useState('')
  const [files, setFiles] = useState<BucketFile[]>([])
  const [folders, setFolders] = useState<string[]>([])
  const [breadcrumbs, setBreadcrumbs] = useState<Array<{ name: string; path: string }>>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isCached, setIsCached] = useState(false)
  const [selectedFile, setSelectedFile] = useState<BucketFile | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [isLoadingPreview, setIsLoadingPreview] = useState(false)
  const [isGalleryOpen, setIsGalleryOpen] = useState(false)
  const [galleryImageUrl, setGalleryImageUrl] = useState<string | null>(null)
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set())
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false)
  const [bulkDeleteLoading, setBulkDeleteLoading] = useState(false)

  const fetchBucketContents = async (path: string = '', forceRefresh: boolean = false) => {
    setIsLoading(true)
    try {
      const params = new URLSearchParams({ path })
      if (forceRefresh) {
        params.set('refresh', 'true')
      }

      const response = await fetch(`/api/bucket/list?${params}`)
      const result: BucketListResponse = await response.json()

      if (result.success && result.data) {
        setFiles(result.data.files)
        setFolders(result.data.folders)
        setBreadcrumbs(result.data.breadcrumbs)
        setCurrentPath(result.data.currentPath)
        setIsCached(result.cached || false)
        setSelectedFiles(new Set()) // Clear selection when navigating
      } else {
        toast.error(result.error || 'Failed to load bucket contents')
      }
    } catch (error) {
      console.error('Error fetching bucket contents:', error)
      toast.error('Failed to load bucket contents')
    } finally {
      setIsLoading(false)
    }
  }

  const handleRefresh = () => {
    fetchBucketContents(currentPath, true)
    toast.success('Refreshed bucket contents')
  }

  const handleFolderClick = (folderName: string) => {
    const newPath = currentPath ? `${currentPath}/${folderName}` : folderName
    setCurrentPath(newPath)
    fetchBucketContents(newPath)
  }

  const handleBreadcrumbClick = (path: string) => {
    setCurrentPath(path)
    fetchBucketContents(path)
  }

  const handleFileSelect = async (file: BucketFile) => {
    setSelectedFile(file)
    setPreviewUrl(null)
    
    // Only fetch preview URL for images and audio files
    if (file.isImage || file.isAudio) {
      setIsLoadingPreview(true)
      try {
        const response = await fetch(`/api/bucket/preview?key=${encodeURIComponent(file.key)}`)
        const result: BucketPreviewResponse = await response.json()
        
        if (result.success && result.data) {
          setPreviewUrl(result.data.previewUrl)
        } else {
          toast.error('Failed to generate preview URL')
        }
      } catch (error) {
        console.error('Preview error:', error)
        toast.error('Failed to load preview')
      } finally {
        setIsLoadingPreview(false)
      }
    }
  }

  const handleDownload = async (file: BucketFile) => {
    try {
      const response = await fetch(`/api/bucket/download?key=${encodeURIComponent(file.key)}`)
      const result: BucketDownloadResponse = await response.json()

      if (result.success && result.data) {
        // Create a temporary link to trigger download
        const link = document.createElement('a')
        link.href = result.data.downloadUrl
        link.download = result.data.filename
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        toast.success('Download started')
      } else {
        toast.error(result.error || 'Failed to generate download URL')
      }
    } catch (error) {
      console.error('Download error:', error)
      toast.error('Failed to download file')
    }
  }

  const handleDelete = async (file: BucketFile) => {
    try {
      const response = await fetch('/api/bucket/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ key: file.key }),
      })

      const result = await response.json()

      if (result.success) {
        toast.success('File deleted successfully')
        // Refresh the current directory
        fetchBucketContents(currentPath, true)
        // Clear selected file if it was deleted
        if (selectedFile?.key === file.key) {
          setSelectedFile(null)
        }
      } else {
        toast.error(result.error || 'Failed to delete file')
      }
    } catch (error) {
      console.error('Delete error:', error)
      toast.error('Failed to delete file')
    }
  }

  const handleBulkDelete = async () => {
    setBulkDeleteLoading(true)
    try {
      const selectedFilesArray = Array.from(selectedFiles)
      const deletePromises = selectedFilesArray.map(async (fileKey) => {
        const response = await fetch('/api/bucket/delete', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ key: fileKey }),
        })
        return response.json()
      })

      const results = await Promise.all(deletePromises)
      const failedDeletes = results.filter(result => !result.success)

      if (failedDeletes.length > 0) {
        toast.error(`Failed to delete ${failedDeletes.length} file(s)`)
      } else {
        toast.success(`Successfully deleted ${selectedFiles.size} file(s)`)
      }

      setSelectedFiles(new Set())
      setShowBulkDeleteDialog(false)
      
      // Clear selected file if it was deleted
      if (selectedFile && selectedFiles.has(selectedFile.key)) {
        setSelectedFile(null)
      }
      
      // Refresh the current directory
      fetchBucketContents(currentPath, true)
    } catch (error) {
      console.error('Bulk delete error:', error)
      toast.error('Failed to delete files')
    } finally {
      setBulkDeleteLoading(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('Copied to clipboard')
  }

  const handleImageClick = (url: string) => {
    setGalleryImageUrl(url)
    setIsGalleryOpen(true)
  }

  const closeGallery = () => {
    setIsGalleryOpen(false)
    setGalleryImageUrl(null)
  }

  useEffect(() => {
    fetchBucketContents()
  }, [])

  return (
    <div className="flex h-full">
      {/* Main Content */}
      <div className="flex-1 flex flex-col space-y-6 p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-3xl font-bold tracking-tight">Bucket Storage</h1>
              <Badge variant="outline" className="text-sm">
                file
              </Badge>
            </div>
            <p className="text-muted-foreground">
              Manage files in your Cloudflare R2 bucket
            </p>
          </div>
          <div className="flex items-center gap-2">
            {selectedFiles.size > 0 && (
              <Button 
                onClick={() => setShowBulkDeleteDialog(true)}
                variant="outline"
                size="sm"
              >
                <Trash2 className="h-4 w-4 mr-2 text-destructive" />
                Delete ({selectedFiles.size})
              </Button>
            )}
            {isCached && (
              <Badge variant="secondary">Cached</Badge>
            )}
            <Button onClick={handleRefresh} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Breadcrumbs */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2 text-sm">
              {breadcrumbs.map((crumb, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleBreadcrumbClick(crumb.path)}
                    className="h-auto p-1 font-medium"
                  >
                    {index === 0 ? (
                      <Home className="h-4 w-4" />
                    ) : (
                      crumb.name
                    )}
                  </Button>
                  {index < breadcrumbs.length - 1 && (
                    <ChevronRight className="h-4 w-4 text-muted-foreground" />
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* File Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Folder className="h-5 w-5" />
              Contents
              <Badge variant="outline">{files.length + folders.length} items</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                Loading...
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    {files.length > 0 && (
                      <TableHead className="w-12">
                        <input 
                          type="checkbox" 
                          className="rounded border-input"
                          checked={selectedFiles.size > 0 && selectedFiles.size === files.length}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedFiles(new Set(files.map(file => file.key)))
                            } else {
                              setSelectedFiles(new Set())
                            }
                          }}
                        />
                      </TableHead>
                    )}
                    <TableHead>Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Size</TableHead>
                    <TableHead>Modified</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {folders.map((folder) => (
                    <TableRow
                      key={folder}
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleFolderClick(folder)}
                    >
                      {files.length > 0 && (
                        <TableCell className="w-12">
                          {/* Empty cell for folder rows to align with file checkboxes */}
                        </TableCell>
                      )}
                      <TableCell className="flex items-center gap-2">
                        <Folder className="h-4 w-4 text-blue-500" />
                        {folder}
                      </TableCell>
                      <TableCell>Folder</TableCell>
                      <TableCell>—</TableCell>
                      <TableCell>—</TableCell>
                      <TableCell></TableCell>
                    </TableRow>
                  ))}
                  {files.map((file) => (
                    <TableRow
                      key={file.key}
                      className={`cursor-pointer hover:bg-muted/50 ${
                        selectedFile?.key === file.key ? 'bg-muted' : ''
                      } ${
                        selectedFiles.has(file.key) ? 'bg-accent/50' : ''
                      }`}
                      onClick={() => handleFileSelect(file)}
                    >
                      <TableCell className="w-12">
                        <input 
                          type="checkbox"
                          className="rounded border-input"
                          checked={selectedFiles.has(file.key)}
                          onChange={(e) => {
                            e.stopPropagation()
                            const newSelected = new Set(selectedFiles)
                            if (e.target.checked) {
                              newSelected.add(file.key)
                            } else {
                              newSelected.delete(file.key)
                            }
                            setSelectedFiles(newSelected)
                          }}
                        />
                      </TableCell>
                      <TableCell className="flex items-center gap-2">
                        {file.isImage ? (
                          <Image className="h-4 w-4 text-green-500" />
                        ) : file.isAudio ? (
                          <Music className="h-4 w-4 text-purple-500" />
                        ) : (
                          <File className="h-4 w-4 text-gray-500" />
                        )}
                        {file.name}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {file.mimeType?.split('/')[1]?.toUpperCase() || 'FILE'}
                        </Badge>
                      </TableCell>
                      <TableCell>{formatFileSize(file.size)}</TableCell>
                      <TableCell>{formatDate(file.lastModified)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleDownload(file)
                            }}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <Trash2 className="h-4 w-4 text-destructive" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Delete File</DialogTitle>
                                <DialogDescription>
                                  Are you sure you want to delete &quot;{file.name}&quot;? This action cannot be undone.
                                </DialogDescription>
                              </DialogHeader>
                              <DialogFooter>
                                <DialogClose asChild>
                                  <Button variant="outline">Cancel</Button>
                                </DialogClose>
                                <Button
                                  onClick={() => handleDelete(file)}
                                  variant="destructive"
                                >
                                  Delete
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Preview Panel */}
      {selectedFile && (
        <>
          <Separator orientation="vertical" className="mx-0" />
          <div className="w-80 p-6 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">File Preview</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Preview Section */}
                <div className="space-y-3">
                  {selectedFile.isImage ? (
                    <div className="space-y-2">
                      {isLoadingPreview ? (
                        <div className="flex items-center justify-center h-48 bg-muted rounded-lg">
                          <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
                        </div>
                      ) : previewUrl ? (
                        <img
                          src={previewUrl}
                          alt={selectedFile.name}
                          className="w-full rounded-lg border object-contain max-h-48 cursor-pointer hover:opacity-90 transition-opacity"
                          onClick={() => handleImageClick(previewUrl)}
                          onError={(e) => {
                            (e.target as HTMLImageElement).style.display = 'none'
                          }}
                        />
                      ) : (
                        <div className="flex items-center justify-center h-48 bg-muted rounded-lg">
                          <Image className="h-8 w-8 text-muted-foreground" />
                        </div>
                      )}
                    </div>
                  ) : selectedFile.isAudio ? (
                    <div className="space-y-2">
                      {isLoadingPreview ? (
                        <div className="flex items-center justify-center h-20 bg-muted rounded-lg">
                          <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
                        </div>
                      ) : previewUrl ? (
                        <div className="p-4 bg-muted rounded-lg">
                          <audio controls className="w-full">
                            <source src={previewUrl} type="audio/mp4" />
                            <source src={previewUrl} type="audio/mpeg" />
                            Your browser does not support the audio element.
                          </audio>
                        </div>
                      ) : (
                        <div className="flex items-center justify-center h-20 bg-muted rounded-lg">
                          <Music className="h-8 w-8 text-muted-foreground" />
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-32 bg-muted rounded-lg">
                      <File className="h-8 w-8 text-muted-foreground" />
                    </div>
                  )}
                </div>

                {/* File Information */}

                <div className="space-y-2 text-sm">
                  <div>
                    <strong>Name:</strong> {selectedFile.name}
                  </div>
                  <div>
                    <strong>Size:</strong> {formatFileSize(selectedFile.size)}
                  </div>
                  <div>
                    <strong>Type:</strong> {selectedFile.mimeType}
                  </div>
                  <div>
                    <strong>Modified:</strong> {formatDate(selectedFile.lastModified)}
                  </div>
                </div>

                {/* Full Link Preview Section */}
                {(selectedFile.isImage || selectedFile.isAudio) && (
                  <div className="space-y-2 pt-4 border-t">
                    <div className="flex items-center gap-2">
                      <strong className="text-sm">Full Link:</strong>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(getPublicUrl(selectedFile.key))}
                        title="Copy to clipboard"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.open(getPublicUrl(selectedFile.key), '_blank')}
                        title="Open in new tab"
                      >
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    </div>
                    <div className="text-xs text-muted-foreground break-all bg-muted p-2 rounded">
                      {getPublicUrl(selectedFile.key)}
                    </div>
                  </div>
                )}

              </CardContent>
            </Card>
          </div>
        </>
      )}

      {/* Full Photo Gallery Modal */}
      <Dialog open={isGalleryOpen} onOpenChange={setIsGalleryOpen}>
        <DialogContent className="max-w-[95vw] max-h-[95vh] p-0 bg-black/90 border-none">
          <DialogHeader className="sr-only">
            <DialogTitle>Image Gallery</DialogTitle>
            <DialogDescription>
              Full-size image viewer for the selected file
            </DialogDescription>
          </DialogHeader>
          <div className="relative w-full h-full flex items-center justify-center">
            {galleryImageUrl && (
              <img
                src={galleryImageUrl}
                alt="Full size preview"
                className="max-w-full max-h-full object-contain"
                onClick={closeGallery}
              />
            )}
            <DialogClose asChild>
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-4 right-4 text-white hover:bg-white/20 rounded-full h-10 w-10"
                onClick={closeGallery}
              >
                ✕
              </Button>
            </DialogClose>
          </div>
        </DialogContent>
      </Dialog>

      {/* Bulk Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={showBulkDeleteDialog}
        onOpenChange={setShowBulkDeleteDialog}
        title="Delete Multiple Files"
        description={
          <div className="space-y-2">
            <p>Are you sure you want to delete {selectedFiles.size} selected file(s)?</p>
            <p className="text-sm text-red-600">This action cannot be undone.</p>
          </div>
        }
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleBulkDelete}
        loading={bulkDeleteLoading}
        variant="destructive"
      />
    </div>
  )
}
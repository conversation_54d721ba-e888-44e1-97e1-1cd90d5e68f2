import { NextRequest, NextResponse } from 'next/server'
import { createBearerToken } from '@/lib/jwt'

export async function POST(request: NextRequest) {
  try {
    const { sql, params } = await request.json()

    if (!sql) {
      return NextResponse.json(
        { success: false, error_msg: 'SQL query is required' },
        { status: 400 }
      )
    }

    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    const webhookToken = process.env.CHHLAT_DB_WEBHOOK_TOKEN

    if (!webhookUrl || !webhookToken) {
      return NextResponse.json(
        { success: false, error_msg: 'Database webhook configuration is missing' },
        { status: 500 }
      )
    }

    const bearerToken = createBearerToken()

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Authorization': bearerToken,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sql,
        params: params || [],
      }),
    })

    // Check if response has content before trying to parse JSON
    const responseText = await response.text()

    if (!responseText || responseText.trim() === '') {
      // Empty response likely means empty table, return success with empty array
      return NextResponse.json({
        success: true,
        body: [],
        error_msg: null,
      })
    }

    let result
    try {
      result = JSON.parse(responseText)
    } catch (parseError) {
      return NextResponse.json(
        {
          success: false,
          error_msg: 'Invalid JSON response from database webhook',
          body: null
        },
        { status: 502 }
      )
    }

    if (!response.ok) {
      return NextResponse.json(
        { 
          success: false, 
          error_msg: result.error_msg || 'Database query failed',
          body: null 
        },
        { status: response.status }
      )
    }

    return NextResponse.json({
      success: result.success || true,
      body: result.body || result,
      error_msg: result.error_msg || null,
    })
  } catch (error) {
    console.error('Database API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error_msg: 'Internal server error',
        body: null 
      },
      { status: 500 }
    )
  }
}
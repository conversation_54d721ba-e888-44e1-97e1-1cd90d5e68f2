import { NextRequest, NextResponse } from 'next/server'
import { createBearerToken } from '@/lib/jwt'

export async function POST(request: NextRequest) {
  try {
    const { operation, tableName, data, conditions } = await request.json()
    
    if (!operation || !tableName) {
      return NextResponse.json(
        { success: false, error: 'Missing operation or tableName' },
        { status: 400 }
      )
    }

    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    const webhookToken = process.env.CHHLAT_DB_WEBHOOK_TOKEN

    if (!webhookUrl || !webhookToken) {
      return NextResponse.json(
        { success: false, error: 'Database webhook configuration is missing' },
        { status: 500 }
      )
    }

    let sql = ''
    let params: any[] = []

    switch (operation) {
      case 'insert':
        if (!data) {
          return NextResponse.json(
            { success: false, error: 'Missing data for insert operation' },
            { status: 400 }
          )
        }
        
        const columns = Object.keys(data)
        const values = Object.values(data)
        const placeholders = values.map((_, i) => `$${i + 1}`).join(', ')
        
        sql = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`
        params = values
        break

      case 'update':
        if (!data || !conditions) {
          return NextResponse.json(
            { success: false, error: 'Missing data or conditions for update operation' },
            { status: 400 }
          )
        }
        
        const updateColumns = Object.keys(data)
        const updateValues = Object.values(data)
        const conditionColumns = Object.keys(conditions)
        const conditionValues = Object.values(conditions)
        
        const setClause = updateColumns.map((col, i) => `${col} = $${i + 1}`).join(', ')
        const whereClause = conditionColumns.map((col, i) => `${col} = $${updateValues.length + i + 1}`).join(' AND ')
        
        sql = `UPDATE ${tableName} SET ${setClause} WHERE ${whereClause}`
        params = [...updateValues, ...conditionValues]
        break

      case 'delete':
        if (!conditions) {
          return NextResponse.json(
            { success: false, error: 'Missing conditions for delete operation' },
            { status: 400 }
          )
        }
        
        const deleteConditionColumns = Object.keys(conditions)
        const deleteConditionValues = Object.values(conditions)
        const deleteWhereClause = deleteConditionColumns.map((col, i) => `${col} = $${i + 1}`).join(' AND ')
        
        sql = `DELETE FROM ${tableName} WHERE ${deleteWhereClause}`
        params = deleteConditionValues
        break

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid operation' },
          { status: 400 }
        )
    }

    const bearerToken = createBearerToken()

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Authorization': bearerToken,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sql,
        params,
      }),
    })

    const responseText = await response.text()

    if (!responseText || responseText.trim() === '') {
      return NextResponse.json({
        success: true,
        data: [],
        message: `${operation} operation completed successfully`
      })
    }

    let result
    try {
      result = JSON.parse(responseText)
    } catch (parseError) {
      return NextResponse.json(
        { success: false, error: 'Invalid JSON response from database webhook' },
        { status: 502 }
      )
    }

    if (!response.ok) {
      return NextResponse.json(
        { 
          success: false, 
          error: result.error_msg || 'Database operation failed'
        },
        { status: response.status }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.body || result,
      message: `${operation} operation completed successfully`
    })

  } catch (error) {
    console.error('CRUD API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
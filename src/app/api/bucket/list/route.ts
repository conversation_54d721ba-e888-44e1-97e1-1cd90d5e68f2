import { NextRequest, NextResponse } from 'next/server'
import { S3Client, ListObjectsV2Command } from '@aws-sdk/client-s3'
import { serverCache } from '@/lib/cache'
import { BucketFile, BucketListResponse } from '@/types'

// Initialize S3 client for Cloudflare R2
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
  },
})


function getMimeType(key: string): string {
  const ext = key.split('.').pop()?.toLowerCase()
  const mimeTypes: Record<string, string> = {
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    gif: 'image/gif',
    webp: 'image/webp',
    mp3: 'audio/mpeg',
    wav: 'audio/wav',
    m4a: 'audio/mp4',
    ogg: 'audio/ogg',
  }
  return mimeTypes[ext || ''] || 'application/octet-stream'
}

function createBreadcrumbs(path: string): Array<{ name: string; path: string }> {
  const breadcrumbs = [{ name: 'Root', path: '' }]
  
  if (path) {
    const parts = path.split('/').filter(Boolean)
    let currentPath = ''
    
    parts.forEach((part) => {
      currentPath += (currentPath ? '/' : '') + part
      breadcrumbs.push({ name: part, path: currentPath })
    })
  }
  
  return breadcrumbs
}

export async function GET(request: NextRequest) {
  try {
    // Validate environment variables
    if (!process.env.CLOUDFLARE_R2_ENDPOINT || 
        !process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || 
        !process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY ||
        !process.env.CLOUDFLARE_R2_BUCKET_NAME) {
      return NextResponse.json(
        { success: false, error: 'Cloudflare R2 configuration is missing' },
        { status: 500 }
      )
    }

    const searchParams = request.nextUrl.searchParams
    const path = searchParams.get('path') || ''
    const forceRefresh = searchParams.get('refresh') === 'true'

    // Cache management
    const cacheKey = `bucket-list-${path}`
    
    if (forceRefresh) {
      serverCache.delete(cacheKey)
    }
    
    const cachedData = serverCache.get(cacheKey)
    if (cachedData) {
      return NextResponse.json({
        success: true,
        data: cachedData,
        cached: true
      } as BucketListResponse)
    }

    // Build S3 prefix (path within bucket)
    const prefix = path ? `${path}/` : ''

    // List objects from R2
    const command = new ListObjectsV2Command({
      Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
      Prefix: prefix,
      Delimiter: '/',
      MaxKeys: 1000,
    })

    const response = await s3Client.send(command)

    // Process folders (common prefixes)
    const folders = (response.CommonPrefixes || [])
      .map(prefix => prefix.Prefix?.replace(path ? `${path}/` : '', '').replace('/', '') || '')
      .filter(Boolean)

    // Process files
    const files: BucketFile[] = (response.Contents || [])
      .filter(obj => obj.Key !== prefix) // Exclude the folder itself
      .map(obj => {
        const key = obj.Key || ''
        const name = key.replace(prefix, '')
        const mimeType = getMimeType(key)
        
        return {
          key,
          name,
          size: obj.Size || 0,
          lastModified: obj.LastModified || new Date(),
          type: 'file' as const,
          mimeType,
          isImage: mimeType.startsWith('image/'),
          isAudio: mimeType.startsWith('audio/'),
        }
      })

    const data = {
      files,
      folders,
      currentPath: path,
      breadcrumbs: createBreadcrumbs(path),
    }

    // Cache the results
    const ttl = files.length === 0 && folders.length === 0 ? 2 : 5 // 2min for empty, 5min for data
    serverCache.set(cacheKey, data, ttl)

    return NextResponse.json({
      success: true,
      data,
      cached: false
    } as BucketListResponse)

  } catch (error) {
    console.error('Bucket list error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to list bucket contents' },
      { status: 500 }
    )
  }
}
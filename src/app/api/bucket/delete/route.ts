import { NextRequest, NextResponse } from 'next/server'
import { S3Client, DeleteObjectCommand } from '@aws-sdk/client-s3'
import { serverCache } from '@/lib/cache'
import { BucketDeleteRequest } from '@/types'

// Initialize S3 client for Cloudflare R2
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
  },
})

function invalidateRelatedCaches(key: string) {
  // Extract the directory path from the file key
  const pathParts = key.split('/')
  
  // Invalidate cache for current directory
  if (pathParts.length > 1) {
    const directoryPath = pathParts.slice(0, -1).join('/')
    serverCache.delete(`bucket-list-${directoryPath}`)
  }
  
  // Invalidate root cache
  serverCache.delete('bucket-list-')
  
  // Invalidate parent directories as well
  for (let i = pathParts.length - 2; i >= 0; i--) {
    const parentPath = pathParts.slice(0, i).join('/')
    serverCache.delete(`bucket-list-${parentPath}`)
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Validate environment variables
    if (!process.env.CLOUDFLARE_R2_ENDPOINT || 
        !process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || 
        !process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY ||
        !process.env.CLOUDFLARE_R2_BUCKET_NAME) {
      return NextResponse.json(
        { success: false, error: 'Cloudflare R2 configuration is missing' },
        { status: 500 }
      )
    }

    const body: BucketDeleteRequest = await request.json()
    const { key } = body

    if (!key) {
      return NextResponse.json(
        { success: false, error: 'File key is required' },
        { status: 400 }
      )
    }

    // Delete the file from R2
    const command = new DeleteObjectCommand({
      Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
      Key: key,
    })

    await s3Client.send(command)

    // Invalidate related caches
    invalidateRelatedCaches(key)

    return NextResponse.json({
      success: true,
      message: 'File deleted successfully'
    })

  } catch (error) {
    console.error('Bucket delete error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete file' },
      { status: 500 }
    )
  }
}
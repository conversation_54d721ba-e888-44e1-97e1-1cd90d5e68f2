import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/postgres'

export async function POST(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const body = await request.json()
    const { sql } = body

    if (!sql || typeof sql !== 'string') {
      return NextResponse.json(
        { success: false, error: 'SQL query is required' },
        { status: 400 }
      )
    }

    // Execute query directly with PostgreSQL
    const queryData = await executeQuery(sql.trim(), [])
    
    const executionTime = Date.now() - startTime

    return NextResponse.json({
      success: true,
      data: queryData,
      executionTime,
    })

  } catch (error) {
    console.error('SQL execution error:', error)
    const executionTime = Date.now() - startTime
    
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'SQL execution failed',
        executionTime
      },
      { status: 500 }
    )
  }
}
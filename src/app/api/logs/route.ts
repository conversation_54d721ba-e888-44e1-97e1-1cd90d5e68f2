import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/postgres'
import { serverCache } from '@/lib/cache'

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const rows = parseInt(searchParams.get('rows') || '50')
    
    // Build SQL query with filters
    let sql = 'SELECT * FROM llm_requests'
    const params: any[] = []
    const conditions: string[] = []
    
    // Add filters to WHERE clause
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')
    const conversationId = searchParams.get('conversation_id')
    const clientId = searchParams.get('client_id')
    const taskValue = searchParams.get('task_value')
    
    if (startDate) {
      conditions.push(`created_at >= $${params.length + 1}`)
      params.push(startDate)
    }
    if (endDate) {
      conditions.push(`created_at <= $${params.length + 1}`)
      params.push(endDate)
    }
    if (conversationId) {
      conditions.push(`conversation_id = $${params.length + 1}`)
      params.push(conversationId)
    }
    if (clientId) {
      conditions.push(`client_id = $${params.length + 1}`)
      params.push(clientId)
    }
    if (taskValue) {
      conditions.push(`task = $${params.length + 1}`)
      params.push(taskValue)
    }
    
    if (conditions.length > 0) {
      sql += ' WHERE ' + conditions.join(' AND ')
    }
    
    sql += ' ORDER BY id DESC'
    
    // Add pagination
    const offset = (page - 1) * rows
    sql += ` LIMIT $${params.length + 1} OFFSET $${params.length + 2}`
    params.push(rows, offset)

    // Create cache key based on query parameters
    const cacheKey = `logs_${page}_${rows}_${JSON.stringify({
      startDate,
      endDate,
      conversationId,
      clientId,
      taskValue
    })}`
    
    // Check if refresh is requested
    const forceRefresh = searchParams.get('refresh') === 'true'
    
    if (forceRefresh) {
      // Delete the specific cache entry for this query
      serverCache.delete(cacheKey)
    }
    
    // Check cache first (5 minute cache for logs)
    const cachedData = serverCache.get(cacheKey)
    if (cachedData) {
      return NextResponse.json({
        success: true,
        data: cachedData,
        cached: true,
      })
    }

    // Execute query directly with PostgreSQL
    const logsData = await executeQuery(sql, params)
    
    if (!logsData || logsData.length === 0) {
      const emptyData = {
        data: [],
        page,
        total: 0,
        total_pages: 0,
        limit: rows
      }
      
      // Cache empty results for shorter time
      serverCache.set(cacheKey, emptyData, 2)
      
      return NextResponse.json({
        success: true,
        data: emptyData,
        executionTime: Date.now() - startTime
      })
    }

    // For filtered results, we need to get the actual total count
    let totalCount = logsData.length
    
    // If we have filters, get the actual total count with a separate query
    if (conditions.length > 0) {
      let countSql = 'SELECT COUNT(*) as total FROM llm_requests'
      if (conditions.length > 0) {
        countSql += ' WHERE ' + conditions.join(' AND ')
      }
      
      try {
        const countResult = await executeQuery(countSql, params.slice(0, -2)) // Remove LIMIT and OFFSET params
        
        if (Array.isArray(countResult) && countResult.length > 0) {
          const firstItem = countResult[0]
          if (firstItem && firstItem.total !== undefined) {
            totalCount = parseInt(firstItem.total) || logsData.length
          }
        }
      } catch (error) {
        console.error('Error getting count:', error)
        totalCount = logsData.length
      }
    } else {
      // For unfiltered results, estimate based on page data
      const hasMorePages = logsData.length === rows
      totalCount = hasMorePages ? (page * rows) + 1 : (page - 1) * rows + logsData.length
    }
    
    const totalPages = Math.ceil(totalCount / rows)
    
    const processedData = {
      data: logsData,
      page: page,
      total: totalCount,
      total_pages: totalPages,
      limit: rows
    }
    
    // Cache the processed result for 5 minutes
    serverCache.set(cacheKey, processedData, 5)
    
    return NextResponse.json({
      success: true,
      data: processedData,
      executionTime: Date.now() - startTime
    })
  } catch (error) {
    console.error('Logs API error:', error)
    const executionTime = Date.now() - startTime
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch logs data',
        details: error instanceof Error ? error.message : 'Unknown error',
        executionTime
      },
      { status: 500 }
    )
  }
}
import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/postgres'

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const { searchParams } = new URL(request.url)
    const username = searchParams.get('username')

    if (!username) {
      return NextResponse.json(
        { success: false, error: 'Username is required' },
        { status: 400 }
      )
    }

    const sql = 'SELECT * FROM clients WHERE username = $1'
    const params = [username]

    // Execute query directly with PostgreSQL
    const users = await executeQuery(sql, params)
    
    if (!users || users.length === 0) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    const client = users[0]
    return NextResponse.json({
      success: true,
      data: client,
      executionTime: Date.now() - startTime
    })

  } catch (error) {
    console.error('Error in GET /api/users/details:', error)
    const executionTime = Date.now() - startTime
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch user details',
        details: error instanceof Error ? error.message : 'Unknown error',
        executionTime
      },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const body = await request.json()
    const { 
      username, 
      company_name,
      contact_email,
      contact_phone,
      status,
      sector,
      lang,
      lang_2,
      usage_used,
      usage_limit,
      contact_need
    } = body

    if (!username) {
      return NextResponse.json(
        { success: false, error: 'Username is required' },
        { status: 400 }
      )
    }
    
    // Build dynamic UPDATE SQL with only provided fields
    const updateFields: string[] = []
    const params: any[] = []
    let paramIndex = 1

    // Only update fields that are provided and editable
    if (company_name !== undefined) {
      updateFields.push(`company_name = $${paramIndex++}`)
      params.push(company_name)
    }
    if (contact_email !== undefined) {
      updateFields.push(`contact_email = $${paramIndex++}`)
      params.push(contact_email)
    }
    if (contact_phone !== undefined) {
      updateFields.push(`contact_phone = $${paramIndex++}`)
      params.push(contact_phone)
    }
    if (status !== undefined) {
      updateFields.push(`status = $${paramIndex++}`)
      params.push(status)
    }
    if (sector !== undefined) {
      updateFields.push(`sector = $${paramIndex++}`)
      params.push(sector)
    }
    if (lang !== undefined) {
      updateFields.push(`lang = $${paramIndex++}`)
      params.push(lang)
    }
    if (lang_2 !== undefined) {
      updateFields.push(`lang_2 = $${paramIndex++}`)
      params.push(lang_2)
    }
    if (usage_used !== undefined) {
      updateFields.push(`usage_used = $${paramIndex++}`)
      params.push(usage_used)
    }
    if (usage_limit !== undefined) {
      updateFields.push(`usage_limit = $${paramIndex++}`)
      params.push(usage_limit)
    }
    if (contact_need !== undefined) {
      updateFields.push(`contact_need = $${paramIndex++}`)
      params.push(contact_need)
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No fields to update' },
        { status: 400 }
      )
    }

    // Add username parameter for WHERE clause
    params.push(username)
    const sql = `UPDATE clients SET ${updateFields.join(', ')} WHERE username = $${paramIndex} RETURNING *`

    // Execute query directly with PostgreSQL
    const users = await executeQuery(sql, params)
    
    if (!users || users.length === 0) {
      return NextResponse.json(
        { success: false, error: 'User not found or no changes made' },
        { status: 404 }
      )
    }

    const updatedClient = users[0]
    return NextResponse.json({
      success: true,
      data: updatedClient,
      executionTime: Date.now() - startTime
    })

  } catch (error) {
    console.error('Error in PUT /api/users/details:', error)
    const executionTime = Date.now() - startTime
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update user details',
        details: error instanceof Error ? error.message : 'Unknown error',
        executionTime
      },
      { status: 500 }
    )
  }
}
import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/postgres'
import { calculateNextBillingDate, getUsageLimitForPlan } from '@/lib/billing-utils'
import { createBearerToken } from '@/lib/jwt'

export async function POST(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const body = await request.json()
    const { action, username, plan, billCycle, message_count } = body

    if (action === 'update_plan') {
      
      if (!username || !plan || !billCycle) {
        return NextResponse.json(
          { success: false, error: 'Username, plan, and bill cycle are required' },
          { status: 400 }
        )
      }

      try {
        const usageLimit = getUsageLimitForPlan(plan)
        const startDate = new Date().toISOString()
        const nextBillingDate = calculateNextBillingDate(billCycle)

        // Single atomic operation - returns result: 1 for success, 0 for failure
        const transactionSql = `
          WITH updated_client AS (
            UPDATE clients 
            SET plan_type = $1, billing_cycle = $2, usage_limit = $3, start_date = $4, next_billing_date = $5 
            WHERE username = $6
            RETURNING client_id, username
          ),
          inserted_subscription AS (
            INSERT INTO client_subscriptions (client_id, plan_type, billing_cycle, start_date, next_billing_date)
            SELECT client_id, $1, $2, $4, $5 
            FROM updated_client
            RETURNING id
          )
          SELECT 
            CASE 
              WHEN EXISTS (SELECT 1 FROM updated_client) AND EXISTS (SELECT 1 FROM inserted_subscription)
              THEN 1  -- Success: both client update and subscription insert worked
              ELSE 0  -- Failure: either client not found or subscription insert failed
            END as result,
            (SELECT client_id FROM updated_client LIMIT 1) as client_id;
        `
        
        // Execute query directly with PostgreSQL
        const result = await executeQuery(transactionSql, [plan, billCycle, usageLimit, startDate, nextBillingDate, username])
        
        // Check if operation was successful
        if (!result || result.length === 0 || result[0].result !== 1) {
          return NextResponse.json(
            { success: false, error: 'User not found or plan update failed' },
            { status: 404 }
          )
        }

        // Success - send webhook notification after PostgreSQL operation
        const client_id = result[0].client_id
        
        // Send webhook notification (don't fail main operation if webhook fails)
        try {
          const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
          if (webhookUrl) {
            const bearerToken = createBearerToken()
            const webhookResponse = await fetch(webhookUrl, {
              method: 'POST',
              headers: {
                'Authorization': bearerToken,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                operation: 'update_plan',
                client_id: client_id,
                plan: plan,
                usage_limit: usageLimit
              }),
            })
            
            if (!webhookResponse.ok) {
              console.warn('Webhook notification failed for update_plan:', await webhookResponse.text())
            }
          }
        } catch (webhookError) {
          console.warn('Webhook notification error for update_plan:', webhookError)
        }

        // Return success response with Cambodia time for admin UI
        const nextBillingDateObj = new Date(nextBillingDate)
        const cambodiaTime = new Date(nextBillingDateObj.getTime() + (7 * 60 * 60 * 1000))
        
        return NextResponse.json({
          success: true,
          data: {
            username: username,
            plan: plan,
            next_billing_date: cambodiaTime.toISOString().replace('Z', '+07:00'),
          },
          executionTime: Date.now() - startTime
        })
      } catch (error) {
        console.error('Error in update_plan operation:', error)
        const executionTime = Date.now() - startTime
        
        return NextResponse.json(
          { 
            success: false, 
            error: 'Failed to update plan',
            details: error instanceof Error ? error.message : 'Unknown error',
            executionTime
          },
          { status: 500 }
        )
      }
    }

    if (action === 'add_message') {
      
      if (!username || !message_count) {
        return NextResponse.json(
          { success: false, error: 'Username and message count are required' },
          { status: 400 }
        )
      }

      // Validate message count is a positive number
      if (typeof message_count !== 'number' || message_count <= 0) {
        return NextResponse.json(
          { success: false, error: 'Message count must be a positive number' },
          { status: 400 }
        )
      }

      try {
        // Get current usage_limit, update it, and return result with old and new limits plus client_id
        const updateSql = `
          WITH current_data AS (
            SELECT COALESCE(usage_limit, 0) as old_limit, client_id
            FROM clients 
            WHERE username = $2
          ),
          updated_data AS (
            UPDATE clients 
            SET usage_limit = COALESCE(usage_limit, 0) + $1 
            WHERE username = $2
            RETURNING COALESCE(usage_limit, 0) as new_limit, client_id
          )
          SELECT 
            CASE 
              WHEN updated_data.new_limit IS NOT NULL THEN 1
              ELSE 0
            END as result,
            current_data.old_limit,
            updated_data.new_limit,
            updated_data.client_id
          FROM current_data
          FULL OUTER JOIN updated_data ON updated_data.client_id = current_data.client_id;
        `
        
        // Execute query directly with PostgreSQL
        const result = await executeQuery(updateSql, [message_count, username])
        
        // Check if operation was successful
        if (!result || result.length === 0 || result[0].result !== 1) {
          return NextResponse.json(
            { success: false, error: 'User not found or message addition failed' },
            { status: 404 }
          )
        }

        // Success - send webhook notification after PostgreSQL operation
        const client_id = result[0].client_id
        const new_limit = result[0].new_limit
        
        // Send webhook notification (don't fail main operation if webhook fails)
        try {
          const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
          if (webhookUrl) {
            const bearerToken = createBearerToken()
            const webhookResponse = await fetch(webhookUrl, {
              method: 'POST',
              headers: {
                'Authorization': bearerToken,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                operation: 'add_message',
                client_id: client_id,
                new_limit: new_limit
              }),
            })
            
            if (!webhookResponse.ok) {
              console.warn('Webhook notification failed for add_message:', await webhookResponse.text())
            }
          }
        } catch (webhookError) {
          console.warn('Webhook notification error for add_message:', webhookError)
        }

        // Return success response
        return NextResponse.json({
          success: true,
          data: {
            username: username,
            messages_added: message_count,
            message: `Successfully added ${message_count} messages to ${username}`
          },
          executionTime: Date.now() - startTime
        })
      } catch (error) {
        console.error('Error in add_message operation:', error)
        const executionTime = Date.now() - startTime
        
        return NextResponse.json(
          { 
            success: false, 
            error: 'Failed to add messages',
            details: error instanceof Error ? error.message : 'Unknown error',
            executionTime
          },
          { status: 500 }
        )
      }
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Invalid action',
        executionTime: Date.now() - startTime
      },
      { status: 400 }
    )
  } catch (error) {
    console.error('User API error:', error)
    const executionTime = Date.now() - startTime
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
        executionTime
      },
      { status: 500 }
    )
  }
}


import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/postgres'
import { serverCache } from '@/lib/cache'
import { RequestLogData } from '@/types'

// Helper function to process raw logs into analytics data
function processLogsToAnalytics(logs: RequestLogData[]) {
  if (!logs || logs.length === 0) {
    return {
      total_requests: 0,
      total_cost: 0,
      total_tokens: 0,
      average_cost_per_request: 0,
      daily_requests: [],
      daily_costs: [],
      recent_logs: []
    }
  }

  // Calculate totals
  let totalCost = 0
  let totalTokens = 0
  const dailyData = new Map<string, { requests: number; cost: number }>()

  logs.forEach((log, index) => {
    // Convert string values to numbers and handle NaN
    const logCost = Number(log.total_cost) || 0
    const inputTokens = Number(log.input_tokens) || 0
    const outputTokens = Number(log.output_tokens) || 0
    const cacheReadTokens = Number(log.cache_read_tokens) || 0
    const cacheWriteTokens = Number(log.cache_write_tokens) || 0
    const thinkingTokens = Number(log.thinking_tokens) || 0
    const audioInputTokens = Number(log.audio_input_tokens) || 0

    // Sum up costs
    totalCost += logCost

    // Sum up all token types (excluding cache read tokens as they're already counted in input tokens)
    const logTokens = inputTokens + outputTokens + 
                     cacheWriteTokens + thinkingTokens + audioInputTokens
    totalTokens += logTokens

    // Group by date for daily metrics
    // Handle both Date objects (from PostgreSQL) and string dates (from webhooks)
    let date: string
    const createdAt = log.created_at as any // Type assertion to handle Date objects from PostgreSQL
    if (typeof createdAt === 'string') {
      date = createdAt.split('T')[0] // Extract YYYY-MM-DD from string
    } else if (createdAt instanceof Date) {
      date = createdAt.toISOString().split('T')[0] // Extract YYYY-MM-DD from Date object
    } else {
      // Fallback for other formats
      date = new Date(createdAt).toISOString().split('T')[0]
    }
    const dayData = dailyData.get(date) || { requests: 0, cost: 0 }
    dayData.requests += 1
    dayData.cost += logCost
    dailyData.set(date, dayData)
  })

  // Convert daily data to arrays and sort by date
  const daily_requests = Array.from(dailyData.entries())
    .map(([date, data]) => ({ date, count: data.requests }))
    .sort((a, b) => a.date.localeCompare(b.date))

  const daily_costs = Array.from(dailyData.entries())
    .map(([date, data]) => ({ date, cost: data.cost }))
    .sort((a, b) => a.date.localeCompare(b.date))

  return {
    total_requests: logs.length,
    total_cost: totalCost,
    total_tokens: totalTokens,
    average_cost_per_request: logs.length > 0 ? totalCost / logs.length : 0,
    daily_requests,
    daily_costs,
    recent_logs: logs // Include all logs within date range for detailed analysis
  }
}

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')

    // Create cache key based on date range
    const cacheKey = `analytics_${startDate || 'all'}_${endDate || 'all'}`
    
    // Check if refresh is requested
    const forceRefresh = searchParams.get('refresh') === 'true'
    
    if (forceRefresh) {
      // Delete the specific cache entry for this query
      serverCache.delete(cacheKey)
    }
    
    // Check cache first (5 minute cache for analytics)
    const cachedData = serverCache.get(cacheKey)
    if (cachedData) {
      return NextResponse.json({
        success: true,
        data: cachedData,
        cached: true,
      })
    }

    // Build SQL query with optional date filtering using direct PostgreSQL
    let sql = 'SELECT * FROM llm_requests'
    const params: any[] = []
    
    if (startDate || endDate) {
      const conditions = []
      if (startDate) {
        conditions.push(`created_at >= $${params.length + 1}`)
        params.push(startDate)
      }
      if (endDate) {
        conditions.push(`created_at <= $${params.length + 1}`)
        params.push(endDate)
      }
      sql += ' WHERE ' + conditions.join(' AND ')
    }
    
    sql += ' ORDER BY created_at DESC'

    // Execute query directly with PostgreSQL
    const rawLogs = await executeQuery(sql, params)
    
    if (!rawLogs || rawLogs.length === 0) {
      const emptyAnalytics = processLogsToAnalytics([])
      
      // Cache empty results for shorter time
      serverCache.set(cacheKey, emptyAnalytics, 2)
      
      return NextResponse.json({
        success: true,
        data: emptyAnalytics,
        executionTime: Date.now() - startTime
      })
    }

    // Process raw logs into analytics data
    const analyticsData = processLogsToAnalytics(rawLogs)

    // Cache the processed result for 5 minutes
    serverCache.set(cacheKey, analyticsData, 5)

    return NextResponse.json({
      success: true,
      data: analyticsData,
      executionTime: Date.now() - startTime
    })
  } catch (error) {
    console.error('Analytics API error:', error)
    const executionTime = Date.now() - startTime
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch analytics data',
        details: error instanceof Error ? error.message : 'Unknown error',
        executionTime
      },
      { status: 500 }
    )
  }
}
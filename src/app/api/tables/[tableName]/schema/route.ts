import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/postgres'

// Whitelist of allowed tables for security
const ALLOWED_TABLES = [
  'clients',
  'plans',
  'client_subscriptions',
  'client_credentials',
  'chat_histories',
  'faqs',
  'welcome_chat',
  'photos',
  'connected_acc',
  'error_logs',
  'error_trigger_logs',
  'incoming_webhook_logs',
  'llm_requests',
  'model_pricing'
]

function validateTableName(tableName: string): boolean {
  return ALLOWED_TABLES.includes(tableName)
}

// GET - Get table schema information
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ tableName: string }> }
) {
  const startTime = Date.now()
  
  try {
    const { tableName } = await params
    
    // Validate table name
    if (!validateTableName(tableName)) {
      return NextResponse.json({
        success: false,
        error: `Table '${tableName}' is not allowed`,
        code: 'TABLE_NOT_ALLOWED'
      }, { status: 400 })
    }

    // Get column information from information_schema
    const columnsQuery = `
      SELECT 
        column_name,
        data_type,
        is_nullable,
        column_default,
        character_maximum_length,
        numeric_precision,
        numeric_scale,
        udt_name
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
        AND table_name = $1
      ORDER BY ordinal_position
    `

    // Get primary key information
    const primaryKeysQuery = `
      SELECT kcu.column_name
      FROM information_schema.table_constraints tc
      JOIN information_schema.key_column_usage kcu 
        ON tc.constraint_name = kcu.constraint_name
        AND tc.table_schema = kcu.table_schema
      WHERE tc.constraint_type = 'PRIMARY KEY'
        AND tc.table_schema = 'public'
        AND tc.table_name = $1
      ORDER BY kcu.ordinal_position
    `

    // Get foreign key information
    const foreignKeysQuery = `
      SELECT 
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM information_schema.table_constraints AS tc
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
        AND tc.table_schema = kcu.table_schema
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
        AND ccu.table_schema = tc.table_schema
      WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_schema = 'public'
        AND tc.table_name = $1
    `

    // Execute all queries in parallel
    const [columnsResult, primaryKeysResult, foreignKeysResult] = await Promise.all([
      executeQuery(columnsQuery, [tableName]),
      executeQuery(primaryKeysQuery, [tableName]),
      executeQuery(foreignKeysQuery, [tableName])
    ])

    // Process results
    const primaryKeys = primaryKeysResult.map(row => row.column_name)
    const foreignKeysMap = new Map()
    
    foreignKeysResult.forEach(row => {
      foreignKeysMap.set(row.column_name, {
        referencedTable: row.foreign_table_name,
        referencedColumn: row.foreign_column_name
      })
    })

    // Format column information
    const columns = columnsResult.map(col => ({
      column_name: col.column_name,
      data_type: col.data_type,
      is_nullable: col.is_nullable === 'YES',
      column_default: col.column_default,
      character_maximum_length: col.character_maximum_length,
      numeric_precision: col.numeric_precision,
      numeric_scale: col.numeric_scale,
      udt_name: col.udt_name,
      is_primary_key: primaryKeys.includes(col.column_name),
      is_foreign_key: foreignKeysMap.has(col.column_name),
      foreign_table: foreignKeysMap.get(col.column_name)?.referencedTable || null,
      foreign_column: foreignKeysMap.get(col.column_name)?.referencedColumn || null
    }))

    // Format foreign keys array
    const foreignKeys = Array.from(foreignKeysMap.entries()).map(([column, ref]) => ({
      column,
      referencedTable: ref.referencedTable,
      referencedColumn: ref.referencedColumn
    }))

    const executionTime = Date.now() - startTime

    return NextResponse.json({
      success: true,
      data: {
        tableName,
        columns,
        primaryKeys,
        foreignKeys
      },
      executionTime
    })

  } catch (error) {
    console.error('Table schema error:', error)
    const executionTime = Date.now() - startTime
    
    return NextResponse.json({
      success: false,
      error: 'Failed to get table schema',
      details: error instanceof Error ? error.message : 'Unknown error',
      executionTime
    }, { status: 500 })
  }
}
import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/postgres'

// Whitelist of allowed tables for security
const ALLOWED_TABLES = [
  'clients',
  'plans',
  'client_subscriptions',
  'client_credentials',
  'chat_histories',
  'faqs',
  'welcome_chat',
  'photos',
  'connected_acc',
  'error_logs',
  'error_trigger_logs',
  'incoming_webhook_logs',
  'llm_requests',
  'model_pricing'
]

function validateTableName(tableName: string): boolean {
  return ALLOWED_TABLES.includes(tableName)
}

function buildWhereClause(filters: Record<string, any>, search?: string): { clause: string, params: any[] } {
  const conditions: string[] = []
  const params: any[] = []
  let paramIndex = 1

  // Add filter conditions
  for (const [column, value] of Object.entries(filters)) {
    if (value !== null && value !== undefined && value !== '') {
      // Validate column name to prevent SQL injection
      if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(column)) {
        continue // Skip invalid column names
      }
      conditions.push(`${column} = $${paramIndex}`)
      params.push(value)
      paramIndex++
    }
  }

  // Add search condition (basic implementation)
  if (search && search.trim()) {
    // This is a simplified search - in production you'd want to specify searchable columns
    conditions.push(`(
      CAST(id AS TEXT) ILIKE $${paramIndex} OR
      CASE 
        WHEN column_name IN ('name', 'title', 'email', 'type', 'status') 
        THEN CAST(column_name AS TEXT) ILIKE $${paramIndex}
        ELSE FALSE 
      END
    )`)
    params.push(`%${search}%`)
    paramIndex++
  }

  return {
    clause: conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '',
    params
  }
}

// GET - Get total row count
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ tableName: string }> }
) {
  const startTime = Date.now()
  
  try {
    const { tableName } = await params
    
    // Validate table name
    if (!validateTableName(tableName)) {
      return NextResponse.json({
        success: false,
        error: `Table '${tableName}' is not allowed`,
        code: 'TABLE_NOT_ALLOWED'
      }, { status: 400 })
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const search = searchParams.get('search') || undefined
    const filterParam = searchParams.get('filter')
    
    let filter: Record<string, any> = {}
    if (filterParam) {
      try {
        filter = JSON.parse(filterParam)
      } catch (e) {
        return NextResponse.json({
          success: false,
          error: 'Invalid filter format. Must be valid JSON.',
          code: 'INVALID_FILTER'
        }, { status: 400 })
      }
    }

    // Build WHERE clause (same as main query to ensure consistency)
    const { clause: whereClause, params: whereParams } = buildWhereClause(filter, search)
    
    // Build and execute count query
    const countQuery = `SELECT COUNT(*) as total FROM ${tableName} ${whereClause}`
    
    const result = await executeQuery(countQuery, whereParams)
    
    const count = parseInt(result[0]?.total || '0')
    const executionTime = Date.now() - startTime

    return NextResponse.json({
      success: true,
      count,
      tableName,
      executionTime
    })

  } catch (error) {
    console.error('Table count error:', error)
    const executionTime = Date.now() - startTime
    
    return NextResponse.json({
      success: false,
      error: 'Failed to get table count',
      details: error instanceof Error ? error.message : 'Unknown error',
      executionTime
    }, { status: 500 })
  }
}
import { NextRequest, NextResponse } from 'next/server'
import { executeQuery, testConnection } from '@/lib/postgres'

export async function GET(request: NextRequest) {
  try {
    // Test basic connection first
    const connectionTest = await testConnection()
    if (!connectionTest.success) {
      return NextResponse.json({
        success: false,
        error: 'Database connection failed',
        details: connectionTest.message
      }, { status: 500 })
    }

    // Test queries for clients and logs tables
    const results: any = {
      connection: connectionTest,
      queries: {}
    }

    try {
      // Test clients table
      const clientsQuery = 'SELECT * FROM clients LIMIT 5'
      const clients = await executeQuery(clientsQuery)
      results.queries.clients = {
        success: true,
        count: clients.length,
        data: clients
      }
    } catch (error) {
      results.queries.clients = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }

    try {
      // Test logs table
      const logsQuery = 'SELECT * FROM logs LIMIT 5'
      const logs = await executeQuery(logsQuery)
      results.queries.logs = {
        success: true,
        count: logs.length,
        data: logs
      }
    } catch (error) {
      results.queries.logs = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }

    // Get table information
    try {
      const tablesQuery = `
        SELECT table_name, column_name, data_type 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name IN ('clients', 'logs')
        ORDER BY table_name, ordinal_position
      `
      const tableInfo = await executeQuery(tablesQuery)
      results.tableInfo = tableInfo
    } catch (error) {
      results.tableInfo = {
        error: error instanceof Error ? error.message : 'Could not fetch table info'
      }
    }

    return NextResponse.json({
      success: true,
      message: 'PostgreSQL test completed',
      results
    })

  } catch (error) {
    console.error('PostgreSQL test error:', error)
    return NextResponse.json({
      success: false,
      error: 'PostgreSQL test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
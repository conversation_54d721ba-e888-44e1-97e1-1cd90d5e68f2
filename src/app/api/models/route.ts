import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/postgres'

export async function GET() {
  const startTime = Date.now()
  
  try {
    // Execute query directly with PostgreSQL
    const sql = 'SELECT * FROM model_pricing ORDER BY updated_at DESC'
    const modelsData = await executeQuery(sql, [])
    
    return NextResponse.json({
      success: true,
      data: modelsData,
      executionTime: Date.now() - startTime
    })
  } catch (error) {
    console.error('Models GET API error:', error)
    const executionTime = Date.now() - startTime
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch models data',
        details: error instanceof Error ? error.message : 'Unknown error',
        executionTime
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const modelData = await request.json()
    
    // Validate required fields
    const requiredFields = ['name', 'model', 'provider', 'key']
    for (const field of requiredFields) {
      if (!modelData[field]) {
        return NextResponse.json(
          { success: false, error: `${field} is required` },
          { status: 400 }
        )
      }
    }

    // All pricing fields that should be included in INSERT
    const allPricingFields = [
      'name', 'model', 'provider', 'key', 
      'input_cost_per_token', 'output_cost_per_token',
      'cache_read_cost_per_token', 'cache_write_cost_per_token', 
      'thinking_cost_per_token', 'audio_input_cost_per_token'
    ]
    
    // Build column list and params (always include all fields)
    const columns = [...allPricingFields]
    const params = [
      modelData.name,
      modelData.model, 
      modelData.provider,
      modelData.key,
      parseFloat(modelData.input_cost_per_token?.toFixed(9)) || 0,
      parseFloat(modelData.output_cost_per_token?.toFixed(9)) || 0,
      parseFloat(modelData.cache_read_cost_per_token?.toFixed(9)) || 0,
      parseFloat(modelData.cache_write_cost_per_token?.toFixed(9)) || 0,
      parseFloat(modelData.thinking_cost_per_token?.toFixed(9)) || 0,
      parseFloat(modelData.audio_input_cost_per_token?.toFixed(9)) || 0
    ]
    
    // Generate dynamic SQL with correct number of placeholders
    const placeholders = params.map((_, index) => `$${index + 1}`).join(', ')
    const columnList = columns.join(', ')
    const sql = `INSERT INTO model_pricing (${columnList}) VALUES (${placeholders})`
    
    // Execute query directly with PostgreSQL
    await executeQuery(sql, params)

    return NextResponse.json({
      success: true,
      message: 'Model added successfully',
      executionTime: Date.now() - startTime
    })
  } catch (error) {
    console.error('Models POST API error:', error)
    const executionTime = Date.now() - startTime
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to add model',
        details: error instanceof Error ? error.message : 'Unknown error',
        executionTime
      },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const { id, ...modelData } = await request.json()
    
    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Model ID is required for update' },
        { status: 400 }
      )
    }
    
    // Handle pricing fields - ensure they are proper numbers for PostgreSQL DECIMAL(12,9)
    const numericFields = [
      'input_cost_per_token',
      'output_cost_per_token', 
      'cache_read_cost_per_token',
      'cache_write_cost_per_token',
      'thinking_cost_per_token',
      'audio_input_cost_per_token'
    ]
    
    for (const field of numericFields) {
      if (modelData[field] !== undefined && modelData[field] !== null && modelData[field] !== '') {
        modelData[field] = parseFloat(modelData[field]?.toFixed(9)) || 0
      } else if (modelData[field] !== undefined) {
        // Only set to 0 if the field is being updated (not just missing from request)
        modelData[field] = 0
      }
    }

    // Build dynamic SET clause for UPDATE
    const fields = Object.keys(modelData)
    const setClause = fields.map((field, index) => `${field} = $${index + 1}`).join(', ')
    const params = [...Object.values(modelData), id]
    
    const sql = `UPDATE model_pricing SET ${setClause} WHERE id = $${params.length}`

    // Execute query directly with PostgreSQL
    await executeQuery(sql, params)

    return NextResponse.json({
      success: true,
      message: 'Model updated successfully',
      executionTime: Date.now() - startTime
    })
  } catch (error) {
    console.error('Models PUT API error:', error)
    const executionTime = Date.now() - startTime
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update model',
        details: error instanceof Error ? error.message : 'Unknown error',
        executionTime
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const { id } = await request.json()
    
    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Model ID is required for deletion' },
        { status: 400 }
      )
    }
    
    const sql = 'DELETE FROM model_pricing WHERE id = $1'
    const params = [id]

    // Execute query directly with PostgreSQL
    await executeQuery(sql, params)

    return NextResponse.json({
      success: true,
      message: 'Model deleted successfully',
      executionTime: Date.now() - startTime
    })
  } catch (error) {
    console.error('Models DELETE API error:', error)
    const executionTime = Date.now() - startTime
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete model',
        details: error instanceof Error ? error.message : 'Unknown error',
        executionTime
      },
      { status: 500 }
    )
  }
}
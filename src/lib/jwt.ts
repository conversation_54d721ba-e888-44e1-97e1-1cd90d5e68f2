import jwt from 'jsonwebtoken'

export interface JwtPayload {
  iat: number
  exp: number
}

export function generateJwtToken(): string {
  const secret = process.env.CHHLAT_DB_WEBHOOK_TOKEN

  if (!secret) {
    throw new Error('JWT secret (CHHLAT_DB_WEBHOOK_TOKEN) is not configured')
  }

  const payload = {
    iat: Math.floor(Date.now() / 1000), // Issued at
    exp: Math.floor(Date.now() / 1000) + 60, // Expires in 1 minute
  }

  return jwt.sign(payload, secret, { algorithm: 'HS256' })
}

export function verifyJwtToken(token: string): JwtPayload {
  const secret = process.env.CHHLAT_DB_WEBHOOK_TOKEN

  if (!secret) {
    throw new Error('JWT secret (CHHLAT_DB_WEBHOOK_TOKEN) is not configured')
  }

  try {
    const decoded = jwt.verify(token, secret, { algorithms: ['HS256'] }) as JwtPayload
    return decoded
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new Error('JWT token has expired')
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw new Error('Invalid JWT token')
    } else {
      throw new Error('JWT verification failed')
    }
  }
}

export function createBearerToken(): string {
  const token = generateJwtToken()
  return `Bearer ${token}`
}
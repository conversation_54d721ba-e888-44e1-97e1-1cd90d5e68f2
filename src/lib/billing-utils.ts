// Billing date calculation utility
// Ported from N8N logic with Asia/Phnom_Penh timezone (GMT+7)

export function calculateNextBillingDate(billingCycle: string): string {
  const cycle = billingCycle.toLowerCase();
  
  // Start with UTC time
  const utc = new Date();

  // Add the billing cycle period
  if (cycle === '1 month' || cycle === 'monthly' || cycle === 'trial') {
    utc.setUTCMonth(utc.getUTCMonth() + 1);
  } else if (cycle === '3 months' || cycle === 'quarterly') {
    utc.setUTCMonth(utc.getUTCMonth() + 3);
  } else if (cycle === '6 months') {
    utc.setUTCMonth(utc.getUTCMonth() + 6);
  } else if (cycle === '1 year' || cycle === 'yearly') {
    utc.setUTCMonth(utc.getUTCMonth() + 12);
  } else {
    throw new Error('Invalid billing cycle: ' + cycle);
  }

  // Set time to 00:00 UTC
  utc.setUTCHours(0, 0, 0, 0);

  // Add 7 hours for GMT+7 and format as ISO string
  const phnomPenhTime = new Date(utc.getTime() + (7 * 60 * 60 * 1000));
  
  const pad = (n: number) => String(n).padStart(2, '0');
  const isoString = `${phnomPenhTime.getUTCFullYear()}-${pad(phnomPenhTime.getUTCMonth() + 1)}-${pad(phnomPenhTime.getUTCDate())}T00:00:00+07:00`;
  
  return isoString;
}

export function getUsageLimitForPlan(planType: string): number {
  const plan = planType.toLowerCase();
  
  if (plan === 'intern') {
    return 500;
  } else if (plan === 'assistant') {
    return 1000;
  } else {
    throw new Error('Invalid plan type: ' + planType);
  }
}

export function getCurrentPhnomPenhTime(): string {
  const now = new Date();
  const phnomPenhOffset = 7 * 60; // GMT+7 in minutes
  const local = new Date(now.getTime() + (phnomPenhOffset - now.getTimezoneOffset()) * 60000);
  
  // Format as ISO string with +07:00
  const pad = (n: number) => String(n).padStart(2, '0');
  return `${local.getFullYear()}-${pad(local.getMonth() + 1)}-${pad(local.getDate())}T${pad(local.getHours())}:${pad(local.getMinutes())}:${pad(local.getSeconds())}+07:00`;
}
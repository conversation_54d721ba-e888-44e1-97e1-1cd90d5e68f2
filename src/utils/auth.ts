import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'
import { jwtVerify, createRemoteJWKSet } from 'jose'

export interface AuthResult {
  authenticated: boolean
  authId: string | null
  clientId: string | null
  isAdmin?: boolean
  user?: any
  method?: 'JOSE' | 'GET_CLAIMS'
  duration?: number // in seconds
}

// Cache JWKS for better performance
let jwksCache: ReturnType<typeof createRemoteJWKSet> | null = null
let jwksCacheTime = 0
const JWKS_CACHE_TTL = 10 * 60 * 1000 // 10 minutes

function getJWKS() {
  const now = Date.now()
  if (!jwksCache || (now - jwksCacheTime) > JWKS_CACHE_TTL) {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    jwksCache = createRemoteJWKSet(
      new URL(`${supabaseUrl}/auth/v1/.well-known/jwks.json`)
    )
    jwksCacheTime = now
  }
  return jwksCache
}

/**
 * Fast JWT verification using direct JOSE library (no network calls after JW<PERSON> cache)
 * This is the fastest method for asymmetric JWT verification
 */
async function verifyJWTDirectly(accessToken: string): Promise<AuthResult> {
  try {
    // Check if this is an asymmetric JWT
    const jwtParts = accessToken.split('.')
    if (jwtParts.length !== 3) {
      throw new Error('Invalid JWT format')
    }

    const header = JSON.parse(Buffer.from(jwtParts[0], 'base64url').toString())

    // Only proceed with JWKS verification for asymmetric algorithms
    if (!header.alg || !['RS256', 'ES256', 'PS256'].includes(header.alg)) {
      throw new Error(`Symmetric algorithm: ${header.alg}`)
    }

    const jwks = getJWKS()
    const { payload } = await jwtVerify(accessToken, jwks, {
      issuer: `${process.env.NEXT_PUBLIC_SUPABASE_URL}/auth/v1`
    })

    return {
      authenticated: true,
      authId: payload.sub!,
      clientId: (payload as any).app_metadata?.client_id || null,
      user: {
        id: payload.sub,
        email: payload.email,
        app_metadata: payload.app_metadata || {}
      }
    }
  } catch (error) {
    return {
      authenticated: false,
      authId: null,
      clientId: null
    }
  }
}

/**
 * Authentication verification optimized for middleware-refreshed sessions
 * 1. Direct JWT verification (fastest - no network calls after JWKS cache)
 * 2. Fallback to getClaims() for symmetric JWTs
 * Note: getUser() removed since middleware guarantees fresh sessions
 */
export async function verifyAuth(): Promise<AuthResult> {
  const overallStartTime = Date.now()
  
  try {
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set() {}, // Not needed for server-side read-only operations
          remove() {}, // Not needed for server-side read-only operations
        },
      }
    )

    // Get session - should be fresh thanks to middleware
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session?.access_token) {
      // Should be rare since middleware refreshes sessions
      return { authenticated: false, authId: null, clientId: null }
    }

    // Strategy 1: Try direct JWT verification (fastest for asymmetric JWTs)
    const directResult = await verifyJWTDirectly(session.access_token)

    if (directResult.authenticated) {
      const totalDuration = ((Date.now() - overallStartTime) / 1000).toFixed(3)
      const isAdmin = checkAdminStatus(directResult.user)
      return {
        ...directResult,
        isAdmin,
        method: 'JOSE',
        duration: parseFloat(totalDuration)
      }
    }

    // Strategy 2: Fallback to getClaims() for symmetric JWTs
    const { data: claimsResponse, error: authError } = await supabase.auth.getClaims()

    if (!authError && claimsResponse?.claims?.sub) {
      const totalDuration = ((Date.now() - overallStartTime) / 1000).toFixed(3)
      const user = {
        id: claimsResponse.claims.sub,
        email: claimsResponse.claims.email,
        app_metadata: claimsResponse.claims.app_metadata || {}
      }
      const isAdmin = checkAdminStatus(user)
      return {
        authenticated: true,
        authId: claimsResponse.claims.sub,
        clientId: claimsResponse.claims.app_metadata?.client_id || null,
        isAdmin,
        user,
        method: 'GET_CLAIMS',
        duration: parseFloat(totalDuration)
      }
    }

    // All strategies failed
    return { authenticated: false, authId: null, clientId: null }

  } catch (error) {
    const totalDuration = ((Date.now() - overallStartTime) / 1000).toFixed(3)
    console.error(`[VERIFY_AUTH] ERROR after ${totalDuration}s:`, error)
    return { authenticated: false, authId: null, clientId: null }
  }
}

/**
 * Check if user has admin status
 */
function checkAdminStatus(user: any): boolean {
  // Check if user has admin role in metadata
  const userRole = user?.app_metadata?.role
  if (userRole === 'admin') {
    return true
  }

  // Check if user email matches ADMIN_USER from env
  const adminEmail = process.env.ADMIN_USER
  if (adminEmail && user?.email === adminEmail) {
    return true
  }

  return false
}

/**
 * Simplified admin auth check for API routes and layouts
 */
export async function verifyAdminAuth(): Promise<AuthResult> {
  const result = await verifyAuth()
  return {
    ...result,
    isAdmin: result.authenticated ? checkAdminStatus(result.user) : false
  }
}
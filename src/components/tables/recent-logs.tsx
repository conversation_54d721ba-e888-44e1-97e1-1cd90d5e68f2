'use client'

import { useEffect, useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { RequestLogData } from '@/types'
import { formatTimestamp } from '@/lib/database'

export function RecentLogs() {
  const [logs, setLogs] = useState<RequestLogData[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Generate sample data for demo purposes
    const generateSampleLogs = (): RequestLogData[] => {
      const models = ['gpt-4', 'gpt-3.5-turbo', 'claude-3-sonnet', 'claude-3-haiku']
      const providers = ['openai', 'anthropic']
      const operations = ['completion', 'chat', 'embedding']
      
      return Array.from({ length: 5 }, (_, i) => ({
        id: `log-${i}`,
        conversation_id: `conv-${i}`,
        client_id: `client-${i}`,
        customer_id: `customer-${i}`,
        platform: 'web',
        operation: operations[Math.floor(Math.random() * operations.length)],
        task: 'chat_completion',
        model_code: `model-${i}`,
        model: models[Math.floor(Math.random() * models.length)],
        provider: providers[Math.floor(Math.random() * providers.length)],
        input_tokens: Math.floor(Math.random() * 1000) + 100,
        output_tokens: Math.floor(Math.random() * 500) + 50,
        cache_read_tokens: 0,
        cache_write_tokens: 0,
        thinking_tokens: 0,
        audio_input_tokens: 0,
        input_cost: Math.random() * 0.01,
        output_cost: Math.random() * 0.01,
        cache_read_cost: 0,
        cache_write_cost: 0,
        thinking_cost: 0,
        audio_input_cost: 0,
        total_cost: Math.random() * 0.02,
        flag: 'success',
        created_at: new Date(Date.now() - Math.random() * 86400000).toISOString(),
      }))
    }

    // Simulate loading
    setTimeout(() => {
      setLogs(generateSampleLogs())
      setLoading(false)
    }, 1000)
  }, [])

  if (loading) {
    return (
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-16 bg-muted animate-pulse rounded" />
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-3">
      {logs?.map((log) => (
        <div
          key={log.id}
          className="flex items-center justify-between p-3 border rounded-lg"
        >
          <div className="space-y-1">
            <div className="font-medium text-sm">{log.model}</div>
            <div className="flex gap-2">
              <Badge variant="outline" className="text-xs">
                {log.provider}
              </Badge>
              <Badge variant="secondary" className="text-xs">
                {log.operation}
              </Badge>
            </div>
          </div>
          <div className="text-right space-y-1">
            <div className="font-medium text-sm">
              ${log.total_cost.toFixed(4)}
            </div>
            <div className="text-xs text-muted-foreground">
              {formatTimestamp(log.created_at)}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
'use client'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { RequestLogData, TablePaginationProps } from '@/types'
import { ChevronLeft, ChevronRight, Eye } from 'lucide-react'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { formatTimestamp } from '@/lib/database'

interface RequestLogsTableProps {
  logs: RequestLogData[]
  loading?: boolean
  pagination: TablePaginationProps
  onConversationFilter?: (conversationId: string) => void
  onClientFilter?: (clientId: string) => void
}

export function RequestLogsTable({ 
  logs, 
  loading, 
  pagination, 
  onConversationFilter, 
  onClientFilter 
}: RequestLogsTableProps) {
  const [selectedLog, setSelectedLog] = useState<RequestLogData | null>(null)
  const router = useRouter()

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4,
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num)
  }

  const getStatusBadge = (flag: string) => {
    const variants = {
      success: 'default',
      error: 'destructive',
      timeout: 'secondary',
      rate_limit: 'outline',
    } as const

    return (
      <Badge variant={variants[flag as keyof typeof variants] || 'secondary'}>
        {flag}
      </Badge>
    )
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(10)].map((_, i) => (
          <div key={i} className="h-16 bg-muted animate-pulse rounded" />
        ))}
      </div>
    )
  }

  if (!logs || logs.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No logs found for the selected criteria
      </div>
    )
  }

  const totalPages = Math.ceil(pagination.total / pagination.limit)
  const startPage = Math.max(1, pagination.page - 2)
  const endPage = Math.min(totalPages, pagination.page + 2)

  return (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>ID</TableHead>
            <TableHead>Conversation</TableHead>
            <TableHead>Client</TableHead>
            <TableHead>Platform</TableHead>
            <TableHead>Operation</TableHead>
            <TableHead>Total Tokens</TableHead>
            <TableHead>Total Cost</TableHead>
            <TableHead>Date</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {logs.map((log) => (
            <TableRow key={log.id}>
              <TableCell>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      variant="link"
                      className="p-0 h-auto font-mono text-blue-600 hover:text-blue-800"
                      onClick={() => setSelectedLog(log)}
                    >
                      {log.id || 'N/A'}
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>Request Log Details</DialogTitle>
                      <DialogDescription>
                        Detailed information about this request log entry
                      </DialogDescription>
                    </DialogHeader>
                    {selectedLog && <LogDetails log={selectedLog} />}
                  </DialogContent>
                </Dialog>
              </TableCell>
              <TableCell>
                <Button
                  variant="link"
                  className="p-0 h-auto font-mono text-blue-600 hover:text-blue-800"
                  onClick={() => {
                    if (onConversationFilter && log.conversation_id) {
                      onConversationFilter(log.conversation_id)
                    } else if (log.conversation_id) {
                      router.push(`/dashboard/logs/conversation/${log.conversation_id}`)
                    }
                  }}
                >
                  {log.conversation_id?.slice(0, 8) || 'N/A'}...
                </Button>
              </TableCell>
              <TableCell>
                <Button
                  variant="link"
                  className="p-0 h-auto font-mono text-blue-600 hover:text-blue-800"
                  onClick={() => {
                    if (onClientFilter && log.client_id) {
                      onClientFilter(log.client_id)
                    } else if (log.client_id) {
                      router.push(`/dashboard/logs/client/${log.client_id}`)
                    }
                  }}
                >
                  {log.client_id || 'N/A'}
                </Button>
              </TableCell>
              <TableCell>
                <Badge variant="outline">{log.platform || 'N/A'}</Badge>
              </TableCell>
              <TableCell>{log.operation || 'N/A'}</TableCell>
              <TableCell>
                <div className="text-sm">
                  {formatNumber(
                    (log.input_tokens || 0) + 
                    (log.output_tokens || 0) + 
                    (log.cache_write_tokens || 0) + 
                    (log.thinking_tokens || 0) + 
                    (log.audio_input_tokens || 0)
                  )}
                </div>
              </TableCell>
              <TableCell>{formatCurrency(log.total_cost || 0)}</TableCell>
              <TableCell>
                <div className="text-sm">
                  {log.created_at ? formatTimestamp(log.created_at) : 'N/A'}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Rows per page:</span>
          <Select
            value={pagination.limit.toString()}
            onValueChange={(value) => pagination.onLimitChange(parseInt(value))}
          >
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
              <SelectItem value="200">200</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => pagination.onPageChange(pagination.page - 1)}
            disabled={pagination.page <= 1}
          >
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Button>

          <div className="flex gap-1">
            {Array.from({ length: endPage - startPage + 1 }, (_, i) => {
              const pageNum = startPage + i
              return (
                <Button
                  key={pageNum}
                  variant={pageNum === pagination.page ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => pagination.onPageChange(pageNum)}
                  className="w-10"
                >
                  {pageNum}
                </Button>
              )
            })}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => pagination.onPageChange(pagination.page + 1)}
            disabled={pagination.page >= totalPages}
          >
            Next
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}

function LogDetails({ log }: { log: RequestLogData }) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 6,
    }).format(amount)
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h4 className="font-medium mb-2">Request Info</h4>
          <div className="space-y-2 text-sm">
            <div><strong>ID:</strong> {log.id || 'N/A'}</div>
            <div><strong>Conversation:</strong> {log.conversation_id || 'N/A'}</div>
            <div><strong>Client:</strong> {log.client_id || 'N/A'}</div>
            <div><strong>Customer:</strong> {log.customer_id || 'N/A'}</div>
            <div><strong>Platform:</strong> {log.platform || 'N/A'}</div>
            <div><strong>Operation:</strong> {log.operation || 'N/A'}</div>
            <div><strong>Task:</strong> {log.task || 'N/A'}</div>
          </div>
        </div>

        <div>
          <h4 className="font-medium mb-2">Model Info</h4>
          <div className="space-y-2 text-sm">
            <div><strong>Model:</strong> {log.model || 'N/A'}</div>
            <div><strong>Provider:</strong> {log.provider || 'N/A'}</div>
            <div><strong>Model Code:</strong> {log.model_code || 'N/A'}</div>
            <div><strong>Status:</strong> <Badge variant="outline">{log.flag || 'N/A'}</Badge></div>
            <div><strong>Timestamp:</strong> {log.created_at ? formatTimestamp(log.created_at) : 'N/A'}</div>
          </div>
        </div>
      </div>

      <div>
        <h4 className="font-medium mb-2">Token Usage</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-1">
            <div><strong>Input Tokens:</strong> {(log.input_tokens || 0).toLocaleString()}</div>
            <div><strong>Output Tokens:</strong> {(log.output_tokens || 0).toLocaleString()}</div>
            <div><strong>Cache Read:</strong> {(log.cache_read_tokens || 0).toLocaleString()}</div>
          </div>
          <div className="space-y-1">
            <div><strong>Cache Write:</strong> {(log.cache_write_tokens || 0).toLocaleString()}</div>
            <div><strong>Thinking:</strong> {(log.thinking_tokens || 0).toLocaleString()}</div>
            <div><strong>Audio Input:</strong> {(log.audio_input_tokens || 0).toLocaleString()}</div>
          </div>
        </div>
      </div>

      <div>
        <h4 className="font-medium mb-2">Cost Breakdown</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-1">
            <div><strong>Input Cost:</strong> {formatCurrency(log.input_cost || 0)}</div>
            <div><strong>Output Cost:</strong> {formatCurrency(log.output_cost || 0)}</div>
            <div><strong>Cache Read Cost:</strong> {formatCurrency(log.cache_read_cost || 0)}</div>
          </div>
          <div className="space-y-1">
            <div><strong>Cache Write Cost:</strong> {formatCurrency(log.cache_write_cost || 0)}</div>
            <div><strong>Thinking Cost:</strong> {formatCurrency(log.thinking_cost || 0)}</div>
            <div><strong>Audio Input Cost:</strong> {formatCurrency(log.audio_input_cost || 0)}</div>
          </div>
        </div>
        <div className="mt-2 pt-2 border-t">
          <div className="font-medium">
            <strong>Total Cost:</strong> {formatCurrency(log.total_cost || 0)}
          </div>
        </div>
      </div>
    </div>
  )
}
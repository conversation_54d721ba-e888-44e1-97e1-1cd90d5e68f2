'use client'

import { useState, useEffect, useRef, useMemo } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ChevronLeft, ChevronRight, RefreshCw } from 'lucide-react'
import { toast } from 'sonner'
import { TABLE_SCHEMA_CONFIG } from '@/lib/database'
import { SheetTableDark } from './sheet-table-dark'

interface DatabaseTableViewerProps {
  tableName: string
  displayName: string
  shouldLoad?: boolean
}

interface TableData {
  rows: Record<string, any>[]
  columns: string[]
  totalRows: number
}

export function DatabaseTableViewer({ tableName, displayName, shouldLoad = false }: DatabaseTableViewerProps) {
  const [data, setData] = useState<TableData | null>(null)
  const [loading, setLoading] = useState(false)
  const [hasLoaded, setHasLoaded] = useState(false)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
  })
  const loadingRef = useRef(false)
  
  // Simple cache for paginated data
  const cacheRef = useRef<Map<string, { data: TableData; timestamp: number }>>(new Map())
  const CACHE_TTL = 5 * 60 * 1000 // 5 minutes
  const CACHE_MAX_SIZE = 50 // Maximum number of cached pages

  const fetchTableData = async (forceRefresh = false) => {
    // Prevent duplicate requests
    if (loadingRef.current) return
    
    // Check cache first
    const cacheKey = `${tableName}_${pagination.page}_${pagination.limit}`
    const cachedEntry = cacheRef.current.get(cacheKey)
    const now = Date.now()
    
    if (!forceRefresh && cachedEntry && now - cachedEntry.timestamp < CACHE_TTL) {
      setData(cachedEntry.data)
      setHasLoaded(true)
      return
    }
    
    loadingRef.current = true
    setLoading(true)
    try {
      // Build query parameters for new PostgreSQL endpoint
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        // Don't specify sort - let the API use the table's primary key
        order: 'asc'
      })
      
      // Single API call to new PostgreSQL endpoint
      const response = await fetch(`/api/tables/${tableName}?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      const result = await response.json()

      if (result.success) {
        const rows = result.data || []
        const totalCount = result.pagination?.total || 0

        if (rows.length > 0) {
          const columns = Object.keys(rows[0]) || []

          const tableData = {
            rows: rows,
            columns: columns,
            totalRows: rows.length,
          }
          setData(tableData)
          setPagination(prev => ({ ...prev, total: totalCount }))
          setHasLoaded(true)
          
          // Cache the result with size limit
          if (cacheRef.current.size >= CACHE_MAX_SIZE) {
            // Remove oldest entry
            const firstKey = cacheRef.current.keys().next().value
            if (firstKey) cacheRef.current.delete(firstKey)
          }
          cacheRef.current.set(cacheKey, { data: tableData, timestamp: now })
        } else {
          // Handle empty result but table exists
          const tableSchema = TABLE_SCHEMA_CONFIG[tableName]
          const columns = tableSchema ? Object.keys(tableSchema.columns) : []

          const tableData = {
            rows: [],
            columns: columns || [],
            totalRows: 0,
          }
          setData(tableData)
          setPagination(prev => ({ ...prev, total: totalCount }))
          setHasLoaded(true)
          
          // Cache the result with size limit
          if (cacheRef.current.size >= CACHE_MAX_SIZE) {
            // Remove oldest entry
            const firstKey = cacheRef.current.keys().next().value
            if (firstKey) cacheRef.current.delete(firstKey)
          }
          cacheRef.current.set(cacheKey, { data: tableData, timestamp: now })
        }
      } else {
        // Handle API error
        const tableSchema = TABLE_SCHEMA_CONFIG[tableName]
        const columns = tableSchema ? Object.keys(tableSchema.columns) : []
        
        setData({
          rows: [],
          columns: columns || [],
          totalRows: 0,
        })
        setPagination(prev => ({ ...prev, total: 0 }))
        setHasLoaded(true)
        toast.error(result.error || 'Failed to fetch table data')
      }
    } catch (error) {
      console.error('Table data fetch error:', error)
      // Set safe empty state on error
      const tableSchema = TABLE_SCHEMA_CONFIG[tableName]
      const columns = tableSchema ? Object.keys(tableSchema.columns) : []
      
      setData({
        rows: [],
        columns: columns || [],
        totalRows: 0,
      })
      setPagination(prev => ({ ...prev, total: 0 }))
      setHasLoaded(true)
      toast.error('An error occurred while fetching table data')
    } finally {
      setLoading(false)
      loadingRef.current = false
    }
  }


  // Clear cache when table name changes
  useEffect(() => {
    cacheRef.current.clear()
    setHasLoaded(false)
    setPagination(prev => ({ ...prev, page: 1, total: 0 }))
  }, [tableName])

  useEffect(() => {
    if (shouldLoad) {
      fetchTableData()
    }
  }, [shouldLoad, tableName, pagination.page, pagination.limit])

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }))
  }

  const handleLimitChange = (newLimit: number) => {
    // Clear cache when limit changes since data structure changes
    cacheRef.current.clear()
    setPagination(prev => ({ ...prev, limit: newLimit, page: 1 }))
  }

  const handleRefresh = () => {
    // Clear cache and force refresh
    cacheRef.current.clear()
    fetchTableData(true)
  }

  const formatCellValue = (value: any): string => {
    if (value === null || value === undefined) {
      return 'NULL'
    }
    if (typeof value === 'object') {
      return JSON.stringify(value)
    }
    if (typeof value === 'boolean') {
      return value ? 'true' : 'false'
    }
    if (typeof value === 'string' && value.length > 100) {
      return value.substring(0, 100) + '...'
    }
    return String(value)
  }

  const getCellComponent = (value: any) => {
    if (value === null || value === undefined) {
      return <span className="text-muted-foreground italic">NULL</span>
    }
    if (typeof value === 'boolean') {
      return (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? 'true' : 'false'}
        </Badge>
      )
    }
    if (typeof value === 'object') {
      return (
        <span className="font-mono text-xs bg-muted px-2 py-1 rounded">
          {JSON.stringify(value)}
        </span>
      )
    }
    return <span>{formatCellValue(value)}</span>
  }

  const totalPages = Math.ceil(pagination.total / pagination.limit)
  const startPage = Math.max(1, pagination.page - 2)
  const endPage = Math.min(totalPages, pagination.page + 2)
  const tableSchema = TABLE_SCHEMA_CONFIG[tableName]

  return (
    <SheetTableDark
      data={data}
      tableName={tableName}
      displayName={displayName}
      primaryKey={tableSchema?.primaryKey}
      shouldLoad={shouldLoad}
      loading={loading}
      hasLoaded={hasLoaded}
      onRefresh={handleRefresh}
      pagination={{
        page: pagination.page,
        limit: pagination.limit,
        total: pagination.total,
        onPageChange: handlePageChange,
        onLimitChange: handleLimitChange,
      }}
    />
  )
}
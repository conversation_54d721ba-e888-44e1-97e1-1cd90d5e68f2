'use client'

import { useState, useMemo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { <PERSON>lt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { 
  Copy, 
  ArrowUpDown, 
  ArrowUp, 
  ArrowDown,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  ChevronDown,
  Key,
  Type,
  Hash,
  Calendar,
  CheckCircle,
  Database,
  RefreshCw,
  Plus,
  Edit,
  Trash2
} from 'lucide-react'
import { toast } from 'sonner'
import { formatTimestamp, formatDate, TABLE_SCHEMA_CONFIG, type ColumnSchema } from '@/lib/database'
import { TableRowForm } from './table-row-form'
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog'
import { ContentPopup } from '@/components/ui/content-popup'

interface SheetTableProps {
  data: {
    rows: Record<string, any>[]
    columns: string[]
    totalRows: number
  } | null
  tableName: string
  primaryKey?: string
  onRefresh?: () => void
  displayName: string
  shouldLoad: boolean
  loading: boolean
  hasLoaded: boolean
  pagination?: {
    page: number
    limit: number
    total: number
    onPageChange: (newPage: number) => void
    onLimitChange: (newLimit: number) => void
  }
}

// Get column schema from PostgreSQL definitions
const getColumnSchema = (tableName: string, columnName: string): ColumnSchema | null => {
  const tableConfig = TABLE_SCHEMA_CONFIG[tableName]
  if (!tableConfig) return null
  return tableConfig.columns[columnName] || null
}

// Detect data presentation type based on schema and value
const detectPresentationType = (value: any, columnName: string, schema: ColumnSchema | null): string => {
  if (!schema) return 'text'
  
  // Use schema type as base
  const schemaType = schema.type.toLowerCase()
  
  // Email detection from value
  if (typeof value === 'string' && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
    return 'email'
  }
  
  // Phone detection from value
  if (typeof value === 'string' && /^[\+]?[1-9][\d]{0,15}$/.test(value.replace(/[\s\-\(\)]/g, ''))) {
    return 'phone'
  }
  
  // URL detection from value
  if (typeof value === 'string' && /^https?:\/\//.test(value)) {
    return 'url'
  }
  
  // Map PostgreSQL types to presentation types
  if (schemaType.includes('uuid')) return 'uuid'
  if (schemaType.includes('timestamp') || schemaType.includes('time')) return 'timestamp'
  if (schemaType.includes('boolean')) return 'boolean'
  if (schemaType.includes('json')) return 'jsonb'
  if (schemaType.includes('integer') || schemaType.includes('serial') || schemaType.includes('real')) return 'number'
  if (schemaType.includes('vector')) return 'vector'
  
  return 'text'
}

const getColumnIcon = (schema: ColumnSchema | null, isPrimary?: boolean) => {
  if (isPrimary) return <Key className="h-3 w-3 text-amber-500" />
  
  if (!schema) return <Type className="h-3 w-3 text-muted-foreground" />
  
  const schemaType = schema.type.toLowerCase()
  
  if (schemaType.includes('uuid')) return <Hash className="h-3 w-3 text-blue-500" />
  if (schemaType.includes('timestamp') || schemaType.includes('time')) return <Calendar className="h-3 w-3 text-purple-500" />
  if (schemaType.includes('integer') || schemaType.includes('serial') || schemaType.includes('real')) return <Hash className="h-3 w-3 text-green-500" />
  if (schemaType.includes('boolean')) return <CheckCircle className="h-3 w-3 text-orange-500" />
  if (schemaType.includes('json')) return <Database className="h-3 w-3 text-indigo-500" />
  if (schemaType.includes('vector')) return <Database className="h-3 w-3 text-pink-500" />
  
  return <Type className="h-3 w-3 text-muted-foreground" />
}

const copyToClipboard = (text: string) => {
  navigator.clipboard.writeText(text)
  toast.success('Copied to clipboard')
}

export function SheetTableDark({
  data,
  tableName,
  displayName,
  primaryKey,
  onRefresh,
  shouldLoad,
  loading,
  hasLoaded,
  pagination,
}: SheetTableProps) {
  const [sortConfig, setSortConfig] = useState<{
    key: string
    direction: 'asc' | 'desc'
  } | null>(null)
  const [selectedRows, setSelectedRows] = useState<Set<number>>(new Set())
  const [showInsertModal, setShowInsertModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [editingRow, setEditingRow] = useState<Record<string, any> | null>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [showContentPopup, setShowContentPopup] = useState(false)
  const [popupContent, setPopupContent] = useState<{
    title: string
    content: any
    contentType: 'text' | 'image' | 'file' | 'array'
    fieldName: string
  } | null>(null)

  // Sort data based on current sort configuration
  const sortedRows = useMemo(() => {
    if (!data?.rows || !sortConfig) {
      return data?.rows || []
    }

    const sorted = [...data.rows].sort((a, b) => {
      const aValue = a[sortConfig.key]
      const bValue = b[sortConfig.key]

      // Handle null/undefined values
      if (aValue == null && bValue == null) return 0
      if (aValue == null) return 1
      if (bValue == null) return -1

      // Convert to comparable values
      let aCompare = aValue
      let bCompare = bValue

      // Handle different data types
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aCompare = aValue.toLowerCase()
        bCompare = bValue.toLowerCase()
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        aCompare = aValue
        bCompare = bValue
      } else {
        // Convert everything else to string for comparison
        aCompare = String(aValue).toLowerCase()
        bCompare = String(bValue).toLowerCase()
      }

      let result = 0
      if (aCompare < bCompare) result = -1
      else if (aCompare > bCompare) result = 1

      return sortConfig.direction === 'desc' ? -result : result
    })

    return sorted
  }, [data?.rows, sortConfig])


  // Get column schemas and presentation types
  const columnInfo = data?.columns?.reduce((acc, column) => {
    const schema = getColumnSchema(tableName, column)
    const sampleValue = data?.rows?.find(row => row[column] !== null && row[column] !== undefined)?.[column]
    const presentationType = detectPresentationType(sampleValue, column, schema)
    acc[column] = { schema, presentationType }
    return acc
  }, {} as Record<string, { schema: ColumnSchema | null, presentationType: string }>) || {}

  const formatCellValue = (value: any, columnName?: string): string => {
    if (value === null || value === undefined) return 'NULL'
    
    if (typeof value === 'boolean') {
      return value ? 'true' : 'false'
    }
    
    // Special handling for timestamp and date columns
    if (columnName && typeof value === 'string') {
      // Force date formatting for known DATE columns (bypass column type detection)
      if (columnName === 'start_date' || columnName === 'next_billing_date') {
        return formatDate(value)
      }
      
      const columnSchema = getColumnSchema(tableName, columnName)
      const columnType = columnSchema?.type.toLowerCase() || ''
      
      // Format timestamp columns (created_at, updated_at, timestamp, etc.)
      if (columnType.includes('timestamp') || columnType.includes('time')) {
        return formatTimestamp(value)
      }
      
      // Format date-only columns (other date fields) 
      if (columnType.includes('date') && !columnType.includes('timestamp')) {
        return formatDate(value)
      }
    }
    
    // Special handling for JSON columns
    if (typeof value === 'object') {
      // Check if this is a JSON column (photo URLs, etc.)
      const isJsonColumn = columnName && getColumnSchema(tableName, columnName)?.type.toLowerCase().includes('json')
      
      if (isJsonColumn) {
        if (Array.isArray(value)) {
          // For arrays, show first item(s) with ellipsis
          if (value.length === 0) return '[]'
          if (value.length === 1) return `["${String(value[0]).substring(0, 20)}..."]`
          if (value.length === 2) return `["${String(value[0]).substring(0, 15)}...", "${String(value[1]).substring(0, 15)}..."]`
          return `["${String(value[0]).substring(0, 15)}...", "${String(value[1]).substring(0, 15)}...", ...]`
        } else {
          // For objects, show first few keys with ellipsis
          const keys = Object.keys(value)
          if (keys.length === 0) return '{}'
          if (keys.length === 1) return `{${keys[0]}: ...}`
          return `{${keys[0]}: ..., ${keys[1]}: ..., ...}`
        }
      }
      
      // For non-JSON columns, stringify and truncate
      const jsonStr = JSON.stringify(value)
      return jsonStr.length > 30 ? jsonStr.substring(0, 30) + '...' : jsonStr
    }
    
    const str = String(value)
    return str.length > 30 ? str.substring(0, 30) + '...' : str
  }

  const getPopupContentType = (columnName: string): 'text' | 'image' | 'file' | 'array' => {
    const columnSchema = getColumnSchema(tableName, columnName)
    return columnSchema?.popupContentType || 'text'
  }

  const shouldShowInPopup = (columnName: string): boolean => {
    const columnSchema = getColumnSchema(tableName, columnName)
    return columnSchema?.showInPopup || false
  }

  const openContentPopup = (value: any, columnName: string) => {
    setPopupContent({
      title: `${columnName} - ${tableName}`,
      content: value,
      contentType: getPopupContentType(columnName),
      fieldName: columnName
    })
    setShowContentPopup(true)
  }

  const renderCell = (value: any, columnName?: string) => {
    const showPopup = columnName && shouldShowInPopup(columnName)
    
    return (
      <span 
        className="text-white text-sm cursor-pointer truncate block hover:bg-accent/20 px-1 py-0.5 rounded"
        onClick={() => {
          if (showPopup && columnName) {
            openContentPopup(value, columnName)
          } else {
            copyToClipboard(String(value || ''))
          }
        }}
        title={showPopup ? 'Click to view in popup' : String(value || '')}
      >
        {formatCellValue(value, columnName)}
      </span>
    )
  }

  const handleSort = (columnKey: string) => {
    let direction: 'asc' | 'desc' = 'asc'
    if (sortConfig && sortConfig.key === columnKey && sortConfig.direction === 'asc') {
      direction = 'desc'
    }
    setSortConfig({ key: columnKey, direction })
  }

  const getSortIcon = (columnKey: string) => {
    if (!sortConfig || sortConfig.key !== columnKey) {
      return <ChevronDown className="h-3 w-3 opacity-30" />
    }
    return sortConfig.direction === 'asc' ? 
      <ChevronUp className="h-3 w-3 text-primary" /> : 
      <ChevronDown className="h-3 w-3 text-primary" />
  }

  const handleFormSubmit = async (data: Record<string, any>) => {
    try {
      if (showInsertModal) {
        const response = await fetch(`/api/tables/${tableName}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            data,
          }),
        })

        const result = await response.json()

        if (result.success) {
          toast.success('Row inserted successfully')
          setShowInsertModal(false)
        } else {
          toast.error(result.error || 'Failed to insert row')
          return
        }
      } else if (showEditModal && editingRow) {
        // Find primary key for update condition
        const tableSchema = TABLE_SCHEMA_CONFIG[tableName]
        const pkColumn = tableSchema?.primaryKey || 'id'
        const pkValue = editingRow[pkColumn]

        if (!pkValue) {
          toast.error('Cannot update row: Primary key not found')
          return
        }

        const response = await fetch(`/api/tables/${tableName}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            id: pkValue,
            data,
          }),
        })

        const result = await response.json()

        if (result.success) {
          toast.success('Row updated successfully')
          setShowEditModal(false)
          setEditingRow(null)
        } else {
          toast.error(result.error || 'Failed to update row')
          return
        }
      }

      if (onRefresh) {
        onRefresh()
      }
    } catch (error) {
      toast.error('An error occurred while saving data')
      console.error('Form submit error:', error)
    }
  }

  const handleDelete = async () => {
    setDeleteLoading(true)
    try {
      const tableSchema = TABLE_SCHEMA_CONFIG[tableName]
      const pkColumn = tableSchema?.primaryKey || 'id'
      
      // Get primary key values for selected rows
      const selectedRowsArray = Array.from(selectedRows)
      const idsToDelete = selectedRowsArray.map((rowIndex) => {
        const row = data?.rows[rowIndex]
        if (!row) return null
        
        const pkValue = row[pkColumn]
        return pkValue
      }).filter(id => id !== null)

      if (idsToDelete.length === 0) {
        toast.error('No valid rows selected for deletion')
        return
      }

      const response = await fetch(`/api/tables/${tableName}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ids: idsToDelete,
        }),
      })

      const result = await response.json()

      if (result.success) {
        toast.success(`Successfully deleted ${result.deletedCount} row(s)`)
      } else {
        toast.error(result.error || 'Failed to delete rows')
      }

      setSelectedRows(new Set())
      setShowDeleteDialog(false)
      
      if (onRefresh) {
        onRefresh()
      }
    } catch (error) {
      toast.error('Failed to delete rows')
      console.error('Delete error:', error)
    } finally {
      setDeleteLoading(false)
    }
  }

  const getDeleteDialogContent = () => {
    const count = selectedRows.size
    if (count === 1) {
      const rowIndex = Array.from(selectedRows)[0]
      const row = data?.rows[rowIndex]
      const primaryKeyValue = row && primaryKey ? row[primaryKey] : 'N/A'
      
      return {
        title: 'Delete Row',
        description: (
          <div className="space-y-2">
            <p>Are you sure you want to delete this row?</p>
            <div className="text-sm text-muted-foreground">
              <p><strong>Table:</strong> {tableName}</p>
              {primaryKey && <p><strong>{primaryKey}:</strong> {primaryKeyValue}</p>}
            </div>
            <p className="text-sm text-red-600">This action cannot be undone.</p>
          </div>
        )
      }
    }
    
    return {
      title: 'Delete Multiple Rows',
      description: (
        <div className="space-y-2">
          <p>Are you sure you want to delete {count} selected rows?</p>
          <div className="text-sm text-muted-foreground">
            <p><strong>Table:</strong> {tableName}</p>
            <p><strong>Rows:</strong> {count} selected</p>
          </div>
          <p className="text-sm text-red-600">This action cannot be undone.</p>
        </div>
      )
    }
  }

  return (
    <div className="rounded-md border">
      {/* Header Section */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-3">
          <h3 className="text-lg font-medium">{displayName}</h3>
          <Button onClick={() => setShowInsertModal(true)} size="sm" variant="outline">
            <Plus className="h-4 w-4 mr-2" />
            Insert
          </Button>
        </div>
        <div className="flex items-center gap-2">
          {selectedRows.size === 1 && (
            <Button 
              onClick={() => {
                const rowIndex = Array.from(selectedRows)[0]
                if (data?.rows[rowIndex]) {
                  setEditingRow(data.rows[rowIndex])
                  setShowEditModal(true)
                }
              }}
              size="sm" 
              variant="outline"
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          )}
          {selectedRows.size > 0 && (
            <Button 
              onClick={() => setShowDeleteDialog(true)}
              size="sm" 
              variant="outline"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete ({selectedRows.size})
            </Button>
          )}
          <Button onClick={onRefresh} disabled={loading} size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Loading...' : 'Refresh'}
          </Button>
        </div>
      </div>


      {/* Main Content Area (Table or Messages) */}
      <div className="">
        {!shouldLoad || (loading && !hasLoaded) ? (
          <div className="text-center py-8 text-muted-foreground">
            <p>Loading data...</p>
          </div>
        ) : (!sortedRows || sortedRows.length === 0) && hasLoaded ? (
          <div className="text-center py-12 text-muted-foreground">
            <p className="text-lg">Empty</p>
            <p className="text-sm mt-2">No data available in this table</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-muted/30 sticky top-0 z-10">
                <tr>
                  {sortedRows && sortedRows.length > 0 && (
                    <th className="w-12 p-3 text-left">
                      <input 
                        type="checkbox" 
                        className="rounded border-input"
                        onChange={(e) => {
                          if (sortedRows && sortedRows.length > 0) {
                            if (e.target.checked) {
                              setSelectedRows(new Set(sortedRows.map((_, i) => i)))
                            } else {
                              setSelectedRows(new Set())
                            }
                          }
                        }}
                        disabled={!sortedRows || sortedRows.length === 0}
                      />
                    </th>
                  )}
                  {(() => {
                    // Fallback: if no columns in data, try to get from schema
                    let columnsToRender = data?.columns
                    if (!columnsToRender || columnsToRender.length === 0) {
                      const tableSchema = TABLE_SCHEMA_CONFIG[tableName]
                      if (tableSchema) {
                        columnsToRender = Object.keys(tableSchema.columns)
                      }
                    }
                    
                    return columnsToRender?.map((column) => {
                      const columnSchema = getColumnSchema(tableName, column)
                      return (
                      <th 
                        key={column} 
                        className="px-3 py-4 text-left border-r border-border last:border-r-0 w-32"
                      >
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-auto p-0 hover:bg-transparent flex items-center gap-2"
                          onClick={() => handleSort(column)}
                          disabled={!sortedRows || sortedRows.length === 0}
                        >
                          <div className="flex items-center gap-1">
                            {getColumnIcon(columnSchema, columnSchema?.isPrimaryKey)}
                            <span className="font-medium text-sm">
                              {column}
                            </span>
                            {getSortIcon(column)}
                          </div>
                        </Button>
                        </th>
                      )
                    }) || []
                  })()}
                </tr>
              </thead>
              <tbody>
                {sortedRows && sortedRows.length > 0 && sortedRows.map((row, rowIndex) => (
                    <tr 
                      key={rowIndex}
                      className={`
                        border-b border-border hover:bg-muted/30 transition-colors
                        ${rowIndex % 2 === 0 ? 'bg-card' : 'bg-muted/10'}
                        ${selectedRows.has(rowIndex) ? 'bg-accent/50' : ''}
                      `}
                    >
                      <td className="p-3">
                        <input 
                          type="checkbox"
                          className="rounded border-input"
                          checked={selectedRows.has(rowIndex)}
                          onChange={(e) => {
                            const newSelected = new Set(selectedRows)
                            if (e.target.checked) {
                              newSelected.add(rowIndex)
                            } else {
                              newSelected.delete(rowIndex)
                            }
                            setSelectedRows(newSelected)
                          }}
                        />
                      </td>
                      {data?.columns?.map((column) => (
                        <td 
                          key={column} 
                          className="px-3 py-2 border-r border-border/50 last:border-r-0 w-32 h-10"
                        >
                          {renderCell(row[column], column)}
                        </td>
                      )) || []}
                    </tr>
                  )) || []}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Enhanced Pagination */}
      {sortedRows && sortedRows.length > 0 && pagination && (
        <div className="flex flex-col sm:flex-row sm:items-center justify-between bg-card border-t p-4 gap-2">
          {/* Left section: Row Count (e.g., "Showing 1-50 of 150 rows") */}
          <div className="flex items-center flex-1 justify-start">
            <span className="text-sm text-muted-foreground whitespace-nowrap">
              {pagination.total === 0 ? 
                'No rows' : 
                `Showing ${((pagination.page - 1) * pagination.limit) + 1}-${Math.min(pagination.page * pagination.limit, pagination.total)} of ${pagination.total} rows`
              }
            </span>
          </div>

          {/* Middle section: Rows per page dropdown */}
          <div className="flex items-center justify-center flex-1">
            <Select
              value={pagination.limit.toString()}
              onValueChange={(value) => pagination.onLimitChange(parseInt(value))}
            >
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
                <SelectItem value="200">200</SelectItem>
                <SelectItem value="500">500</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Right section: Navigation Buttons (Previous/Next) */}
          <div className="flex items-center gap-2 flex-1 justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onPageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
            >
              Previous
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onPageChange(pagination.page + 1)}
              disabled={pagination.page >= Math.ceil(pagination.total / pagination.limit)}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Modals */}
      <TableRowForm
        open={showInsertModal}
        onOpenChange={setShowInsertModal}
        tableName={tableName}
        mode="insert"
        onSubmit={handleFormSubmit}
      />
      
      <TableRowForm
        open={showEditModal}
        onOpenChange={(open) => {
          setShowEditModal(open)
          if (!open) {
            setEditingRow(null)
          }
        }}
        tableName={tableName}
        mode="edit"
        initialData={editingRow || undefined}
        onSubmit={handleFormSubmit}
      />

      <ConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        title={getDeleteDialogContent().title}
        description={getDeleteDialogContent().description}
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleDelete}
        loading={deleteLoading}
        variant="destructive"
      />

      <ContentPopup
        open={showContentPopup}
        onOpenChange={setShowContentPopup}
        title={popupContent?.title || ''}
        content={popupContent?.content}
        contentType={popupContent?.contentType || 'text'}
        fieldName={popupContent?.fieldName}
      />
    </div>
  )
}
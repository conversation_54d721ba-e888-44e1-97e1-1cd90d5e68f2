'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription,
  DialogHeader, 
  DialogTitle, 
  DialogFooter 
} from '@/components/ui/dialog'
import { TABLE_SCHEMA_CONFIG, type ColumnSchema } from '@/lib/database'
import { toast } from 'sonner'

interface TableRowFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  tableName: string
  mode: 'insert' | 'edit'
  initialData?: Record<string, any>
  onSubmit: (data: Record<string, any>) => void
  loading?: boolean
}

export function TableRowForm({
  open,
  onOpenChange,
  tableName,
  mode,
  initialData,
  onSubmit,
  loading = false
}: TableRowFormProps) {
  const [formData, setFormData] = useState<Record<string, any>>({})
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Get table schema
  const tableSchema = TABLE_SCHEMA_CONFIG[tableName]
  const columns = tableSchema ? Object.entries(tableSchema.columns) : []

  // Initialize form data
  useEffect(() => {
    if (mode === 'edit' && initialData) {
      setFormData(initialData)
    } else {
      // Insert mode starts with empty form
      setFormData({})
    }
    setErrors({})
  }, [mode, open])

  const getDefaultValue = (schema: ColumnSchema) => {
    if (schema.type.toLowerCase().includes('boolean')) return false
    if (schema.type.toLowerCase().includes('integer') || schema.type.toLowerCase().includes('real')) return 0
    if (schema.type.toLowerCase().includes('json')) return '{}'
    return ''
  }

  const getInputType = (schema: ColumnSchema) => {
    const type = schema.type.toLowerCase()
    if (type.includes('integer') || type.includes('real') || type.includes('serial')) return 'number'
    if (type.includes('timestamp') || type.includes('date')) return 'datetime-local'
    if (type.includes('boolean')) return 'checkbox'
    return 'text'
  }

  const validateField = (columnName: string, value: any, schema: ColumnSchema) => {
    // Basic validation
    if (schema.nullable === false && (value === null || value === undefined || value === '')) {
      return `${columnName} is required`
    }
    
    // Type-specific validation
    const type = schema.type.toLowerCase()
    if (type.includes('integer') && value !== '' && isNaN(parseInt(value))) {
      return `${columnName} must be a valid integer`
    }
    
    if (type.includes('real') && value !== '' && isNaN(parseFloat(value))) {
      return `${columnName} must be a valid number`
    }
    
    // Skip JSON validation for JSONB fields - we'll handle conversion automatically
    if (type.includes('json') && value !== '' && typeof value === 'string') {
      // For JSONB fields like photo_url and photo_file_path, allow comma-separated input
      // We'll convert it to proper JSON array format during processing
      if (columnName.includes('photo_url') || columnName.includes('photo_file_path')) {
        // Allow comma-separated input - no validation needed
        return null
      }
      
      // For other JSON fields, still validate JSON format
      try {
        JSON.parse(value)
      } catch (error) {
        return `${columnName} must be valid JSON`
      }
    }

    return null
  }

  const handleInputChange = (columnName: string, value: any, schema: ColumnSchema) => {
    setFormData(prev => ({
      ...prev,
      [columnName]: value
    }))

    // Clear error when user starts typing
    if (errors[columnName]) {
      setErrors(prev => ({
        ...prev,
        [columnName]: ''
      }))
    }
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)
    const newErrors: Record<string, string> = {}
    
    try {
      // Validate all fields (skip hidden fields in insert mode)
      columns.forEach(([columnName, schema]) => {
        // Skip validation for fields that are hidden in insert mode
        if (mode === 'insert') {
          const isTimestampField = schema.type.toLowerCase().includes('timestamp') || 
                                 columnName.includes('created_at') || 
                                 columnName.includes('updated_at')
          
          const isSerialField = schema.type.toLowerCase().includes('serial') ||
                               schema.isPrimaryKey ||
                               columnName.toLowerCase() === 'id'
          
          if (isTimestampField || isSerialField) {
            return // Skip validation for hidden fields
          }
        }
        
        const error = validateField(columnName, formData[columnName], schema)
        if (error) {
          newErrors[columnName] = error
        }
      })

      if (Object.keys(newErrors).length > 0) {
        console.log('🔴 Form validation errors:', newErrors)
        setErrors(newErrors)
        toast.error('Please fix the errors before submitting')
        return
      }

      // Process form data
      const processedData = { ...formData }
      
      console.log('✅ Form validation passed, submitting data:', processedData)
      
      // Filter out timestamp fields for update operations (they're auto-managed)
      if (mode === 'edit') {
        Object.keys(processedData).forEach(columnName => {
          const isTimestampField = columnName.includes('created_at') || 
                                 columnName.includes('updated_at') ||
                                 columnName.includes('timestamp')
          if (isTimestampField) {
            delete processedData[columnName]
          }
        })
      }
      
      // Convert empty strings to null for nullable fields
      columns.forEach(([columnName, schema]) => {
        // Skip if field was filtered out
        if (!processedData.hasOwnProperty(columnName)) return
        
        if (schema.nullable && processedData[columnName] === '') {
          processedData[columnName] = null
        }
        
        // Handle type conversions
        const type = schema.type.toLowerCase()
        if (type.includes('integer') && processedData[columnName] !== null && processedData[columnName] !== '') {
          processedData[columnName] = parseInt(processedData[columnName])
        } else if (type.includes('real') && processedData[columnName] !== null && processedData[columnName] !== '') {
          processedData[columnName] = parseFloat(processedData[columnName])
        } else if (type.includes('json') && processedData[columnName] !== null && processedData[columnName] !== '') {
          // Handle JSONB photo fields - convert comma-separated to JSON string
          if (columnName.includes('photo_url') || columnName.includes('photo_file_path')) {
            const stringValue = processedData[columnName].toString().trim()
            if (stringValue) {
              // Check if it's already valid JSON
              try {
                JSON.parse(stringValue)
                // If it parses successfully, it's already JSON - keep as is
                processedData[columnName] = stringValue
              } catch {
                // Not valid JSON, treat as comma-separated and convert
                try {
                  const urlArray = stringValue.split(',').map((url: string) => url.trim()).filter((url: string) => url.length > 0)
                  processedData[columnName] = JSON.stringify(urlArray)
                } catch (stringifyError) {
                  newErrors[columnName] = 'Failed to process photo URLs'
                  console.error('JSONB stringify error:', stringifyError)
                }
              }
            } else {
              processedData[columnName] = JSON.stringify([])
            }
          } else {
            // For other JSON fields, parse normally
            try {
              processedData[columnName] = JSON.parse(processedData[columnName])
            } catch (error) {
              newErrors[columnName] = 'Invalid JSON format'
            }
          }
        }
      })

      // Check for JSON parsing errors
      if (Object.keys(newErrors).length > 0) {
        setErrors(newErrors)
        toast.error('Please fix the JSON format errors')
        return
      }

      await onSubmit(processedData)
    } catch (error) {
      toast.error('An error occurred while processing the form')
      console.error('Form processing error:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderField = (columnName: string, schema: ColumnSchema) => {
    const inputType = getInputType(schema)
    const value = formData[columnName] || ''
    const error = errors[columnName]
    
    // Check if field should be hidden or read-only
    const isTimestampField = schema.type.toLowerCase().includes('timestamp') || 
                           columnName.includes('created_at') || 
                           columnName.includes('updated_at')
    
    const isSerialField = schema.type.toLowerCase().includes('serial') ||
                         schema.isPrimaryKey ||
                         columnName.toLowerCase() === 'id'
    
    // Hide auto-generated fields in insert mode
    if (mode === 'insert' && (isTimestampField || isSerialField)) {
      return null
    }
    
    // Determine if field is read-only
    const isReadOnly = (mode === 'edit' && schema.isPrimaryKey) || 
                      (mode === 'edit' && isTimestampField) ||
                      (mode === 'edit' && isSerialField)

    if (inputType === 'checkbox') {
      return (
        <div key={columnName} className="space-y-2">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id={columnName}
              checked={Boolean(value)}
              onChange={(e) => handleInputChange(columnName, e.target.checked, schema)}
              disabled={isReadOnly}
              className="rounded border-input"
            />
            <Label htmlFor={columnName} className="text-sm font-medium">
              {columnName}
              {schema.nullable === false && <span className="text-red-500 ml-1">*</span>}
            </Label>
          </div>
          {error && <p className="text-red-500 text-xs">{error}</p>}
        </div>
      )
    }

    return (
      <div key={columnName} className="space-y-2">
        <Label htmlFor={columnName} className="text-sm font-medium">
          {columnName}
          {schema.nullable === false && <span className="text-red-500 ml-1">*</span>}
          {isReadOnly && <span className="text-muted-foreground ml-1">(Read-only)</span>}
        </Label>
{schema.type.toLowerCase().includes('json') ? (
          <Textarea
            id={columnName}
            value={value}
            onChange={(e) => handleInputChange(columnName, e.target.value, schema)}
            disabled={isReadOnly}
            className={`${error ? 'border-red-500' : ''} ${isReadOnly ? 'bg-muted/50 text-muted-foreground cursor-not-allowed opacity-60' : ''} min-h-[100px] resize-y`}
            placeholder={
              isReadOnly ? 'Read-only' : 
              (columnName.includes('photo_url') || columnName.includes('photo_file_path')) ? 
                'Enter URLs separated by commas (e.g., url1.jpg, url2.jpg)\nor valid JSON array: ["url1.jpg", "url2.jpg"]' : 
                'Enter valid JSON'
            }
          />
        ) : (
          <Input
            id={columnName}
            type={inputType}
            value={value}
            onChange={(e) => handleInputChange(columnName, e.target.value, schema)}
            disabled={isReadOnly}
            className={`${error ? 'border-red-500' : ''} ${isReadOnly ? 'bg-muted/50 text-muted-foreground cursor-not-allowed opacity-60' : ''}`}
            placeholder={
              isReadOnly ? 'Read-only' : 
              `Enter ${columnName}`
            }
          />
        )}
        {error && <p className="text-red-500 text-xs">{error}</p>}
      </div>
    )
  }

  if (!tableSchema) {
    return null
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {mode === 'insert' ? 'Insert New Row' : 'Edit Row'} - {tableName}
          </DialogTitle>
          <DialogDescription>
            {mode === 'insert' 
              ? `Fill in the form below to add a new row to the ${tableName} table.`
              : `Update the fields below to modify this row in the ${tableName} table.`
            }
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          {columns.map(([columnName, schema]) => renderField(columnName, schema))}
        </div>

        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            disabled={loading || isSubmitting}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={loading || isSubmitting}
          >
            {loading || isSubmitting ? 'Processing...' : mode === 'insert' ? 'Insert' : 'Update'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
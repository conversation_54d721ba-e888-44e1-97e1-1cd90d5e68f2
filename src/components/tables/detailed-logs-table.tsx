'use client'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { RequestLogData, TablePaginationProps } from '@/types'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { formatTimestamp } from '@/lib/database'

interface DetailedLogsTableProps {
  logs: RequestLogData[]
  loading?: boolean
  pagination?: TablePaginationProps
}

export function DetailedLogsTable({ logs, loading, pagination }: DetailedLogsTableProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 6,
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num)
  }

  const getStatusBadge = (flag: string) => {
    const variants = {
      success: 'default',
      error: 'destructive',
      timeout: 'secondary',
      rate_limit: 'outline',
      test: 'outline',
    } as const

    return (
      <Badge variant={variants[flag as keyof typeof variants] || 'secondary'}>
        {flag}
      </Badge>
    )
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(10)].map((_, i) => (
          <div key={i} className="h-16 bg-muted animate-pulse rounded" />
        ))}
      </div>
    )
  }

  if (!logs || logs.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No logs found
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>Conversation</TableHead>
              <TableHead>Client</TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>Platform</TableHead>
              <TableHead>Operation</TableHead>
              <TableHead>Task</TableHead>
              <TableHead>Model</TableHead>
              <TableHead>Provider</TableHead>
              <TableHead>Input Tokens</TableHead>
              <TableHead>Output Tokens</TableHead>
              <TableHead>Cache Read</TableHead>
              <TableHead>Cache Write</TableHead>
              <TableHead>Thinking</TableHead>
              <TableHead>Audio Input</TableHead>
              <TableHead>Total Cost</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {logs.map((log) => (
              <TableRow key={log.id}>
                <TableCell className="font-mono text-xs">{log.id}</TableCell>
                <TableCell className="font-mono text-xs">
                  {log.conversation_id.slice(0, 8)}...
                </TableCell>
                <TableCell className="font-mono text-xs">{log.client_id}</TableCell>
                <TableCell className="font-mono text-xs">{log.customer_id}</TableCell>
                <TableCell>
                  <Badge variant="outline">{log.platform}</Badge>
                </TableCell>
                <TableCell>{log.operation}</TableCell>
                <TableCell>{log.task}</TableCell>
                <TableCell>
                  <Badge variant="outline">{log.model}</Badge>
                </TableCell>
                <TableCell>
                  <Badge variant="secondary">{log.provider}</Badge>
                </TableCell>
                <TableCell className="text-right">
                  {formatNumber(log.input_tokens)}
                </TableCell>
                <TableCell className="text-right">
                  {formatNumber(log.output_tokens)}
                </TableCell>
                <TableCell className="text-right">
                  {formatNumber(log.cache_read_tokens)}
                </TableCell>
                <TableCell className="text-right">
                  {formatNumber(log.cache_write_tokens)}
                </TableCell>
                <TableCell className="text-right">
                  {formatNumber(log.thinking_tokens)}
                </TableCell>
                <TableCell className="text-right">
                  {formatNumber(log.audio_input_tokens)}
                </TableCell>
                <TableCell className="text-right font-medium">
                  {formatCurrency(log.total_cost)}
                </TableCell>
                <TableCell>{getStatusBadge(log.flag)}</TableCell>
                <TableCell>
                  <div className="text-sm">
                    {formatTimestamp(log.created_at)}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      
      {pagination && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Rows per page:</span>
            <Select
              value={pagination.limit.toString()}
              onValueChange={(value) => pagination.onLimitChange(parseInt(value))}
            >
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
                <SelectItem value="200">200</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onPageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>

            <span className="text-sm text-muted-foreground">
              Page {pagination.page} of {Math.ceil(pagination.total / pagination.limit)}
            </span>

            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onPageChange(pagination.page + 1)}
              disabled={pagination.page >= Math.ceil(pagination.total / pagination.limit)}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
      
      {!pagination && (
        <div className="text-sm text-muted-foreground text-center">
          Showing {logs?.length || 0} logs
        </div>
      )}
    </div>
  )
}
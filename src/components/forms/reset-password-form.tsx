'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from 'sonner'
import { Copy, Check } from 'lucide-react'
import { createClient } from '@/lib/supabase'

export function ResetPasswordForm() {
  const [email, setEmail] = useState('')
  const [userId, setUserId] = useState('')
  const [userEmail, setUserEmail] = useState('')
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [message, setMessage] = useState('')
  const [isMounted, setIsMounted] = useState(false)
  const [resetComplete, setResetComplete] = useState(false)
  const [copiedField, setCopiedField] = useState<string | null>(null)
  
  useEffect(() => {
    setIsMounted(true)
  }, [])
  
  const copyToClipboard = (text: string, field: string) => {
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard.writeText(text).then(() => {
        setCopiedField(field)
        toast.success(`${field} copied to clipboard`)
        setTimeout(() => setCopiedField(null), 2000)
      })
    } else {
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      
      try {
        document.execCommand('copy')
        setCopiedField(field)
        toast.success(`${field} copied to clipboard`)
        setTimeout(() => setCopiedField(null), 2000)
      } catch (err) {
        console.error('Failed to copy text: ', err)
        toast.error('Failed to copy to clipboard')
      }
      
      document.body.removeChild(textArea)
    }
  }
  
  const generatePassword = () => {
    const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    let passwordParts = []
    const partLength = 4
    const numParts = 4

    for (let p = 0; p < numParts; p++) {
      let part = ""
      for (let i = 0; i < partLength; i++) {
        const randomIndex = Math.floor(Math.random() * charset.length)
        part += charset[randomIndex]
      }
      passwordParts.push(part)
    }

    return passwordParts.join("-")
  }
  
  const findUserByEmail = async () => {
    if (!email.trim()) {
      setMessage('Please enter an email address')
      setStatus('error')
      return
    }
    
    setStatus('loading')
    setMessage('')
    setResetComplete(false)
    
    try {
      // Get current session for authentication
      const supabase = createClient()
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session?.access_token) {
        setMessage('You must be logged in to perform this action')
        setStatus('error')
        return
      }

      const response = await fetch('/api/users/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({ 
          action: 'find_user',
          email 
        }),
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to find user')
      }
      
      setUserId(data.userId)
      setUserEmail(email)
      setUsername(email.split('@')[0])
      
      const newPassword = generatePassword()
      setPassword(newPassword)
      setStatus('success')
      
    } catch (error: any) {
      console.error('Error finding user:', error)
      setMessage(error.message || 'An error occurred while searching for the user')
      setStatus('error')
    }
  }
  
  const resetPassword = async () => {
    if (!userId || !password) {
      setMessage('Missing user ID or password')
      setStatus('error')
      return
    }
    
    setStatus('loading')
    setMessage('')
    
    try {
      // Get current session for authentication
      const supabase = createClient()
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session?.access_token) {
        setMessage('You must be logged in to perform this action')
        setStatus('error')
        return
      }

      const response = await fetch('/api/users/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({ 
          action: 'update_password',
          userId, 
          password
        }),
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to reset password')
      }
      
      setMessage('Password has been successfully reset')
      setStatus('success')
      setResetComplete(true)
      toast.success('Password reset successful!')
    } catch (error: any) {
      console.error('Error resetting password:', error)
      setMessage(error.message || 'Failed to reset password')
      setStatus('error')
    }
  }
  
  if (!isMounted) {
    return null
  }
  
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="email">User Email</Label>
        <Input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="Enter user email"
          disabled={status === 'loading'}
        />
      </div>
      
      <div className="text-xs text-muted-foreground">
        Note: Only the part before @ will be used to find the user in the clients table
      </div>
      
      <Button
        onClick={findUserByEmail}
        disabled={status === 'loading'}
        className="w-full"
      >
        {status === 'loading' ? 'Searching...' : 'Find User'}
      </Button>
      
      {userId && password && (
        <div className="space-y-4">
          {username && (
            <div className="space-y-2">
              <Label>Username</Label>
              <div className="flex items-center gap-2">
                <Input
                  value={username}
                  readOnly
                  className="bg-muted"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(username, 'username')}
                >
                  {copiedField === 'username' ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          )}
          
          {userEmail && (
            <div className="space-y-2">
              <Label>Email</Label>
              <div className="flex items-center gap-2">
                <Input
                  value={userEmail}
                  readOnly
                  className="bg-muted"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(userEmail, 'email')}
                >
                  {copiedField === 'email' ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          )}
          
          <div className="space-y-2">
            <Label>User ID</Label>
            <div className="flex items-center gap-2">
              <Input
                value={userId}
                readOnly
                className="bg-muted font-mono text-sm"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(userId, 'user ID')}
              >
                {copiedField === 'user ID' ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label>New Password</Label>
            <div className="flex items-center gap-2">
              <Input
                value={password}
                readOnly
                className="bg-muted font-mono text-sm"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(password, 'password')}
              >
                {copiedField === 'password' ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
          
          {!resetComplete && (
            <Button
              onClick={resetPassword}
              disabled={status === 'loading'}
              className="w-full"
            >
              {status === 'loading' ? 'Resetting...' : 'Reset Password'}
            </Button>
          )}
          
          {resetComplete && (
            <div className="p-4 rounded-lg bg-green-500/10 border border-green-500/20 text-green-500">
              <h3 className="font-semibold mb-2">Password Reset Successful</h3>
              <p>The password for {userEmail} has been reset successfully.</p>
            </div>
          )}
        </div>
      )}
      
      {message && !resetComplete && (
        <div className={`p-4 rounded-lg ${
          status === 'error' ? 'bg-red-500/10 border border-red-500/20 text-red-500' : 
          'bg-green-500/10 border border-green-500/20 text-green-500'
        } text-sm`}>
          {message}
        </div>
      )}
    </div>
  )
}
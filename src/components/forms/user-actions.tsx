'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import { UserActionData } from '@/types'

export function UserActions() {
  const [loading, setLoading] = useState(false)
  const [actionForm, setActionForm] = useState({
    action: '',
    user_id: '',
    email: '',
    plan: '',
    message_count: 0,
  })

  const handleAction = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const payload: UserActionData = {
        action: actionForm.action as any,
        user_id: actionForm.user_id || undefined,
        email: actionForm.email || undefined,
        plan: actionForm.plan || undefined,
        message_count: actionForm.message_count || undefined,
      }

      const response = await fetch('/api/users/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      })

      const result = await response.json()

      if (result.success) {
        toast.success(`Action "${actionForm.action}" completed successfully!`)
        setActionForm({
          action: '',
          user_id: '',
          email: '',
          plan: '',
          message_count: 0,
        })
      } else {
        toast.error(result.error || 'Failed to execute action')
      }
    } catch (error) {
      toast.error('An error occurred while executing the action')
    } finally {
      setLoading(false)
    }
  }

  const actionConfigs = {
    upgrade_plan: {
      label: 'Upgrade Plan',
      badge: 'success',
      fields: ['user_id', 'email', 'plan'],
    },
    add_messages: {
      label: 'Add Messages',
      badge: 'info',
      fields: ['user_id', 'email', 'message_count'],
    },
    reset_password: {
      label: 'Reset Password',
      badge: 'warning',
      fields: ['user_id', 'email'],
    },
    clear_data: {
      label: 'Clear User Data',
      badge: 'destructive',
      fields: ['user_id', 'email'],
    },
  }

  const selectedConfig = actionConfigs[actionForm.action as keyof typeof actionConfigs]

  return (
    <form onSubmit={handleAction} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="action">Action</Label>
        <Select
          value={actionForm.action}
          onValueChange={(value) => setActionForm({ ...actionForm, action: value })}
          disabled={loading}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select an action" />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(actionConfigs).map(([key, config]) => (
              <SelectItem key={key} value={key}>
                <div className="flex items-center gap-2">
                  {config.label}
                  <Badge variant={config.badge as any} className="text-xs">
                    {config.badge}
                  </Badge>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {selectedConfig && (
        <>
          <Separator />
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <h4 className="font-medium">{selectedConfig.label}</h4>
              <Badge variant={selectedConfig.badge as any}>
                {selectedConfig.badge}
              </Badge>
            </div>

            {selectedConfig.fields.includes('user_id') && (
              <div className="space-y-2">
                <Label htmlFor="user_id">User ID</Label>
                <Input
                  id="user_id"
                  value={actionForm.user_id}
                  onChange={(e) => setActionForm({ ...actionForm, user_id: e.target.value })}
                  placeholder="Enter user ID"
                  disabled={loading}
                />
              </div>
            )}

            {selectedConfig.fields.includes('email') && (
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={actionForm.email}
                  onChange={(e) => setActionForm({ ...actionForm, email: e.target.value })}
                  placeholder="Enter email address"
                  disabled={loading}
                />
              </div>
            )}

            {selectedConfig.fields.includes('plan') && (
              <div className="space-y-2">
                <Label htmlFor="plan">New Plan</Label>
                <Select
                  value={actionForm.plan}
                  onValueChange={(value) => setActionForm({ ...actionForm, plan: value })}
                  disabled={loading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a plan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="free">Free</SelectItem>
                    <SelectItem value="basic">Basic</SelectItem>
                    <SelectItem value="premium">Premium</SelectItem>
                    <SelectItem value="enterprise">Enterprise</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {selectedConfig.fields.includes('message_count') && (
              <div className="space-y-2">
                <Label htmlFor="message_count">Message Count</Label>
                <Input
                  id="message_count"
                  type="number"
                  value={actionForm.message_count}
                  onChange={(e) => setActionForm({ ...actionForm, message_count: parseInt(e.target.value) })}
                  min="0"
                  placeholder="Number of messages to add"
                  disabled={loading}
                />
              </div>
            )}

            <Button
              type="submit"
              className="w-full"
              disabled={loading || !actionForm.action}
              variant={selectedConfig.badge === 'destructive' ? 'destructive' : 'default'}
            >
              {loading ? 'Executing...' : `Execute ${selectedConfig.label}`}
            </Button>
          </div>
        </>
      )}
    </form>
  )
}
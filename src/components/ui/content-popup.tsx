'use client'

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogClose,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Copy, X, ExternalLink } from 'lucide-react'
import { toast } from 'sonner'

interface ContentPopupProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  content: unknown
  contentType: 'text' | 'image' | 'file' | 'array'
  fieldName?: string
}

export function ContentPopup({
  open,
  onOpenChange,
  title,
  content,
  contentType,
  fieldName
}: ContentPopupProps) {

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('Copied to clipboard')
  }

  const isValidUrl = (str: string): boolean => {
    try {
      new URL(str)
      return true
    } catch {
      return false
    }
  }

  const renderContent = () => {
    if (content === null || content === undefined) {
      return (
        <div className="text-center py-8 text-muted-foreground">
          <p>No content available</p>
        </div>
      )
    }

    // Handle arrays (like photo URLs)
    if (Array.isArray(content)) {
      return (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <p className="text-sm font-medium">{content.length} items</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => copyToClipboard(JSON.stringify(content, null, 2))}
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy All
            </Button>
          </div>
          <div className="max-h-[400px] overflow-y-auto space-y-2">
            {content.map((item, index) => (
              <div key={index} className="flex items-center gap-2 p-2 bg-muted/30 rounded">
                <span className="text-xs text-muted-foreground min-w-[2rem]">
                  {index + 1}.
                </span>
                <span className="flex-1 font-mono text-sm break-all">
                  {String(item)}
                </span>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(String(item))}
                    className="h-8 w-8 p-0"
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                  {isValidUrl(String(item)) && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => window.open(String(item), '_blank')}
                      className="h-8 w-8 p-0"
                    >
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )
    }

    // Handle regular content
    const textContent = String(content)
    
    // Check if this is a question or answer field (don't show character count for these)
    const isQuestionOrAnswer = fieldName && (
      fieldName.includes('question') || 
      fieldName.includes('answer')
    )
    
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          {!isQuestionOrAnswer && (
            <p className="text-sm font-medium">{textContent.length} characters</p>
          )}
          <div className="flex gap-2 ml-auto">
            <Button
              variant="outline"
              size="sm"
              onClick={() => copyToClipboard(textContent)}
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy
            </Button>
            {isValidUrl(textContent) && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(textContent, '_blank')}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                View
              </Button>
            )}
          </div>
        </div>
        <div className="max-h-[400px] overflow-y-auto">
          <div className="whitespace-pre-wrap text-sm p-4 bg-muted/30 rounded font-mono">
            {textContent}
          </div>
        </div>
      </div>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-left">
            {fieldName || title}
          </DialogTitle>
          <DialogClose asChild>
            <Button variant="ghost" size="sm" className="absolute top-2 right-2">
              <X className="h-4 w-4" />
            </Button>
          </DialogClose>
        </DialogHeader>
        <div className="flex-1 overflow-hidden">
          {renderContent()}
        </div>
      </DialogContent>
    </Dialog>
  )
}
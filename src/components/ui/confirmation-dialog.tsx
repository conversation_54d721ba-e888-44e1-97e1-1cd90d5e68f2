'use client'

import { ReactNode } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'

interface ConfirmationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  description: string | ReactNode
  confirmText?: string
  cancelText?: string
  onConfirm: () => void
  onCancel?: () => void
  loading?: boolean
  variant?: 'default' | 'destructive'
}

export function ConfirmationDialog({
  open,
  onOpenChange,
  title,
  description,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
  loading = false,
  variant = 'default'
}: ConfirmationDialogProps) {
  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    } else {
      onOpenChange(false)
    }
  }

  const handleConfirm = () => {
    onConfirm()
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent showCloseButton={false}>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription asChild>
            {typeof description === 'string' ? <span>{description}</span> : description}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={handleCancel} 
            disabled={loading}
          >
            {cancelText}
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={loading}
            variant={variant === 'destructive' ? 'destructive' : 'default'}
          >
            {loading ? 'Processing...' : confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

interface PlanUpdateConfirmationProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  username: string
  newPlan: string
  billingCycle: string
  onConfirm: () => void
  loading?: boolean
}

export function PlanUpdateConfirmation({
  open,
  onOpenChange,
  username,
  newPlan,
  billingCycle,
  onConfirm,
  loading = false
}: PlanUpdateConfirmationProps) {
  return (
    <ConfirmationDialog
      open={open}
      onOpenChange={onOpenChange}
      title="Confirm Plan Update"
      description={
        <div className="space-y-2">
          <p>Are you sure you want to update the plan for this user?</p>
          <div className="space-y-1 text-sm">
            <p><strong>Username:</strong> {username}</p>
            <p><strong>New Plan:</strong> {newPlan}</p>
            <p><strong>Billing Cycle:</strong> {billingCycle}</p>
          </div>
          <p className="text-sm text-muted-foreground">
            This will immediately update the user's plan and reset their billing cycle.
          </p>
        </div>
      }
      confirmText="Update Plan"
      onConfirm={onConfirm}
      loading={loading}
    />
  )
}

interface AddMessageConfirmationProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  username: string
  messageCount: number
  currentUsageLimit: number
  onConfirm: () => void
  loading?: boolean
}

export function AddMessageConfirmation({
  open,
  onOpenChange,
  username,
  messageCount,
  currentUsageLimit,
  onConfirm,
  loading = false
}: AddMessageConfirmationProps) {
  const newUsageLimit = currentUsageLimit + messageCount
  
  return (
    <ConfirmationDialog
      open={open}
      onOpenChange={onOpenChange}
      title="Confirm Add Messages"
      description={
        <div className="space-y-2">
          <p>Are you sure you want to add messages to this user?</p>
          <div className="space-y-1 text-sm">
            <p><strong>Username:</strong> {username}</p>
            <p><strong>Messages to Add:</strong> {messageCount.toLocaleString()}</p>
            <p><strong>Current Usage Limit:</strong> {currentUsageLimit.toLocaleString()}</p>
            <p><strong>New Usage Limit:</strong> {newUsageLimit.toLocaleString()}</p>
          </div>
          <p className="text-sm text-muted-foreground">
            This will immediately add {messageCount.toLocaleString()} messages to the user's account.
          </p>
        </div>
      }
      confirmText="Add Messages"
      onConfirm={onConfirm}
      loading={loading}
    />
  )
}
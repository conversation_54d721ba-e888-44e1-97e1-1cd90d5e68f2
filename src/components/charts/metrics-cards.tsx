'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { AnalyticsOverview } from '@/types'
import { TrendingUp, DollarSign, BarChart3, Zap } from 'lucide-react'

interface MetricsCardsProps {
  analytics: AnalyticsOverview | null
  loading: boolean
}

export function MetricsCards({ analytics, loading }: MetricsCardsProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4,
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num)
  }

  const metrics = [
    {
      title: 'Total Requests',
      value: analytics ? formatNumber(analytics.total_requests) : '---',
      icon: BarChart3,
      description: 'API requests processed',
    },
    {
      title: 'Total Cost',
      value: analytics ? formatCurrency(analytics.total_cost) : '---',
      icon: DollarSign,
      description: 'Total spending',
    },
    {
      title: 'Total Tokens',
      value: analytics ? formatNumber(analytics.total_tokens || 0) : '---',
      icon: Zap,
      description: 'Tokens processed',
    },
    {
      title: 'Avg Cost/Request',
      value: analytics ? formatCurrency(analytics.average_cost_per_request) : '---',
      icon: TrendingUp,
      description: 'Per request cost',
    },
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {metrics.map((metric) => (
        <Card key={metric.title}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {metric.title}
            </CardTitle>
            <metric.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? (
                <div className="h-8 w-20 bg-muted animate-pulse rounded" />
              ) : (
                metric.value
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              {metric.description}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
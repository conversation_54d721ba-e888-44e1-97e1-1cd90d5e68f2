'use client'

import { useEffect, useState } from 'react'
import { AnalyticsCharts } from './analytics-charts'
import { addDays } from 'date-fns'

export function AnalyticsOverview() {
  const [data, setData] = useState<Array<{ date: string; count: number }>>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Generate sample data for demo purposes
    const generateSampleData = () => {
      const days = 7
      const sampleData = []
      
      for (let i = days - 1; i >= 0; i--) {
        const date = addDays(new Date(), -i)
        sampleData.push({
          date: date.toISOString().split('T')[0],
          count: Math.floor(Math.random() * 100) + 20,
        })
      }
      
      return sampleData
    }

    // Simulate loading
    setTimeout(() => {
      setData(generateSampleData())
      setLoading(false)
    }, 1000)
  }, [])

  return (
    <AnalyticsCharts
      data={data}
      type="requests"
      loading={loading}
    />
  )
}
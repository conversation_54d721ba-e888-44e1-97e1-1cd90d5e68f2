'use client'

import { But<PERSON> } from '@/components/ui/button'
import { ChartPeriod } from '@/types'

interface ChartPeriodFilterProps {
  selectedPeriod: ChartPeriod
  onPeriodChange: (period: ChartPeriod) => void
  maxDays?: number
}

const PERIODS: ChartPeriod[] = [
  { label: '1 Week', days: 7 },
  { label: '1 Month', days: 30 },
]

export function ChartPeriodFilter({ 
  selectedPeriod, 
  onPeriodChange, 
  maxDays 
}: ChartPeriodFilterProps) {
  return (
    <div className="flex gap-2">
      {PERIODS.map((period) => {
        const isDisabled = maxDays !== undefined && period.days > maxDays
        const isSelected = selectedPeriod.days === period.days
        
        return (
          <Button
            key={period.days}
            variant={isSelected ? 'default' : 'outline'}
            size="sm"
            onClick={() => onPeriodChange(period)}
            disabled={isDisabled}
            className="text-xs"
          >
            {period.label}
          </Button>
        )
      })}
    </div>
  )
}
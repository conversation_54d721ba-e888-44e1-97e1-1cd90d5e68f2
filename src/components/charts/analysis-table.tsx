'use client'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { AnalysisData } from '@/types'

interface AnalysisTableProps {
  data: AnalysisData[]
  loading: boolean
  title: string
  nameLabel: string
}

export function AnalysisTable({ data, loading, title, nameLabel }: AnalysisTableProps) {
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4,
    }).format(amount)
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-12 bg-muted animate-pulse rounded" />
        ))}
      </div>
    )
  }

  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No {title.toLowerCase()} data available for the selected period
      </div>
    )
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>{nameLabel}</TableHead>
          <TableHead className="text-right">Requests</TableHead>
          <TableHead className="text-right">Tokens</TableHead>
          <TableHead className="text-right">Cost</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {data.map((item, index) => (
          <TableRow key={`${item.name}-${index}`}>
            <TableCell className="font-medium">{item.name}</TableCell>
            <TableCell className="text-right">{formatNumber(item.requests)}</TableCell>
            <TableCell className="text-right">{formatNumber(item.tokens)}</TableCell>
            <TableCell className="text-right">{formatCurrency(item.cost)}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
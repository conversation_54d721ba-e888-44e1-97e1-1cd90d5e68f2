'use client'

import { usePathname } from 'next/navigation'
import { AdminSidebar } from '@/components/layout/admin-sidebar'
import { SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar'

export function DashboardLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()

  // Don't show sidebar on login page
  const isLoginPage = pathname === '/login'
  
  if (isLoginPage) {
    return <>{children}</>
  }

  // For all other pages, show the sidebar
  return (
    <SidebarProvider defaultOpen={false}>
      <AdminSidebar />
      <main className="flex-1 overflow-auto">
        <div className="container mx-auto p-6">
          <div className="mb-4">
            <SidebarTrigger />
          </div>
          {children}
        </div>
      </main>
    </SidebarProvider>
  )
}
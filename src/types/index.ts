export interface User {
  id: string
  email: string
  created_at: string
  updated_at: string
  last_sign_in_at: string | null
}

export interface ModelData {
  id: number
  name: string
  model: string
  provider: string
  key: string
  input_cost_per_token: number
  output_cost_per_token: number
  cache_read_cost_per_token: number
  cache_write_cost_per_token: number
  thinking_cost_per_token: number
  audio_input_cost_per_token: number
  updated_at: string
}

export interface RequestLogData {
  id: string
  conversation_id: string
  client_id: string
  customer_id: string
  platform: string
  operation: string
  task: string
  model_code: string
  model: string
  provider: string
  input_tokens: number
  output_tokens: number
  cache_read_tokens: number
  cache_write_tokens: number
  thinking_tokens: number
  audio_input_tokens: number
  input_cost: number
  output_cost: number
  cache_read_cost: number
  cache_write_cost: number
  thinking_cost: number
  audio_input_cost: number
  total_cost: number
  flag: string
  created_at: string
}

export interface AnalyticsOverview {
  total_requests: number
  total_cost: number
  total_tokens: number
  average_cost_per_request: number
  daily_requests: Array<{ date: string; count: number }>
  daily_costs: Array<{ date: string; cost: number }>
  recent_logs: RequestLogData[] // All logs within the selected date range for detailed analysis
}

export interface AnalysisData {
  name: string
  requests: number
  tokens: number
  cost: number
}

export interface DetailedAnalytics {
  operations: AnalysisData[]
  platforms: AnalysisData[]
  clients: AnalysisData[]
  models: AnalysisData[]
}

export interface ChartPeriod {
  label: string
  days: number
}

export interface UserActionData {
  action: 'upgrade_plan' | 'add_messages' | 'reset_password' | 'clear_data' | 'add_user'
  user_id?: string
  email?: string
  plan?: string
  message_count?: number
  additional_data?: Record<string, any>
}

export interface TablePaginationProps {
  page: number
  limit: number
  total: number
  onPageChange: (page: number) => void
  onLimitChange: (limit: number) => void
}

export interface FilterOptions {
  provider?: string
  model?: string
  platform?: string
  operation?: string
  start_date?: string
  end_date?: string
  customer_id?: string
  flag?: string
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  total_pages: number
}

export interface DatabaseApiResponse {
  success: boolean
  body: Record<string, any>[] | null
  error_msg: string | null
}

export interface DatabaseTableConfig {
  name: string
  displayName: string
  description: string
}

export interface DatabaseTableData {
  rows: Record<string, any>[]
  columns: string[]
  totalRows: number
}

// Database table types based on our schema
export interface Client {
  auth_id: string
  client_id: string
  username: string
  company_name: string | null
  contact_email: string | null
  contact_phone: string | null
  created_at: string
  updated_at: string
  status: string
  created_by: string | null
  sector: string | null
  lang: string | null
  plan_type: string | null
  billing_cycle: string | null
  start_date: string | null
  next_billing_date: string | null
  lang_2: string[] | null
  usage_used: number | null
  usage_limit: number | null
  contact_need: string | null
}

export interface Plan {
  id: number
  created_at: string
  name: string
  price: number
  total_faqs: number
  total_photos: number
  connections: number
  conv: number
}

export interface ClientSubscription {
  id: number
  client_id: string
  plan_type: string
  billing_cycle: string
  start_date: string
  next_billing_date: string | null
  created_at: string
  updated_at: string
}

export interface ClientCredentials {
  id: number
  client_id: string
  fb_url: string | null
  fb_token: string | null
  ig_url: string | null
  ig_token: string | null
  tg_url: string | null
  tg_token: string | null
  wa_url: string | null
  wa_token: string | null
  web_url: string | null
  updated_at: string
  created_at: string
  tg_id: string | null
  tg_name: string | null
  tg_id_name: string | null
  tg_status: number | null
  ig_status: number | null
  fb_status: number | null
  wa_status: number | null
  web_status: number | null
  web_domain: string | null
  fb_id: string | null
  fb_name: string | null
  ig_id: string | null
  ig_name: string | null
  wa_id: string | null
  wa_name: string | null
  web_name: string | null
  tg_biz_id: string | null
  client_tg: string | null
}

export interface ChatHistory {
  id: number
  client_id: string
  customer_id: string
  platform: string
  user_message: string | null
  cleaned_question: string | null
  bot_message: string | null
  question: string | null
  answer: string | null
  question_p: string | null
  answer_p: string | null
  flag: string | null
  created_at: string
  rag_matched: boolean | null
  rag_score: number | null
  chat_type: string | null
  sub_flag: string | null
  lang: string | null
  respond_lang: string | null
  tg_name: string | null
  tg_id: string | null
  audio_duration: number | null
  sector: string | null
  button_clicked: number | null
  feedback_score: number | null
  photo_url: any | null
  answer_audio_duration: number | null
}

export interface FAQ {
  id: number
  client_id: string
  question: string | null
  question_p: string | null
  answer: string | null
  answer_p: string | null
  audio_url: string | null
  photo_url: any | null
  photo_id: string | null
  audio_duration: number | null
  audio_file_path: string | null
  updated_at: string
  created_at: string
  faq_id: string | null
  fb_photo_atmid: any | null
  fb_audio_atmid: string | null
  tg_photo_atmid: any | null
  tg_audio_atmid: string | null
  ig_photo_atmid: any | null
  ig_audio_atmid: string | null
  is_visible: boolean | null
  embedding: any | null
}

export interface Photo {
  id: number
  photo_id: string | null
  client_id: string
  photo_url: any | null
  photo_file_path: any | null
  created_at: string
  updated_at: string
}

export interface WelcomeChat {
  id: number
  client_id: string
  answer: string | null
  answer_p: string | null
  audio_url: string | null
  photo_url: any | null
  photo_id: string | null
  audio_duration: number | null
  audio_file_path: string | null
  updated_at: string
  created_at: string
  chat_id: string | null
  fb_photo_atmid: any | null
  fb_audio_atmid: string | null
  tg_photo_atmid: any | null
  tg_audio_atmid: string | null
  ig_photo_atmid: any | null
  ig_audio_atmid: string | null
}

export interface ConnectedAccount {
  id: number
  platform: string
  acc_id: string
  client_id: string
  created_at: string
}

export interface ErrorLog {
  id: number
  client_id: string
  platform: string | null
  customer_id: string | null
  tg_name: string | null
  tg_id: string | null
  flag: string | null
  sector: string | null
  created_at: string
  plan: string | null
}

export interface ErrorTriggerLog {
  id: number
  workflow: string | null
  url: string | null
  node: string | null
  error_message: string | null
  timestamp: string
}

export interface IncomingWebhookLog {
  id: number
  client_id: string
  platform: string | null
  event_type: string | null
  customer_id: string | null
  flag: string | null
  chat_type: string | null
  message: string | null
  created_at: string
}

// Bucket/File Management Types
export interface BucketFile {
  key: string
  name: string
  size: number
  lastModified: Date
  type: 'file' | 'folder'
  mimeType?: string
  isImage?: boolean
  isAudio?: boolean
}

export interface BucketFolder {
  name: string
  path: string
  files: BucketFile[]
  folders: BucketFolder[]
}

export interface BucketListResponse {
  success: boolean
  data: {
    files: BucketFile[]
    folders: string[]
    currentPath: string
    breadcrumbs: Array<{ name: string; path: string }>
  }
  cached?: boolean
  error?: string
}

export interface BucketDeleteRequest {
  key: string
}

export interface BucketDownloadResponse {
  success: boolean
  data?: {
    downloadUrl: string
    filename: string
  }
  error?: string
}

export interface BucketPreviewResponse {
  success: boolean
  data?: {
    previewUrl: string
    key: string
  }
  error?: string
}
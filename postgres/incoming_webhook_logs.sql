CREATE TABLE public.incoming_webhook_logs (
  id SERIAL NOT NULL,
  client_id text NOT NULL,
  platform TEXT NULL,
  event_type TEXT NULL,
  customer_id TEXT NULL,
  flag TEXT NULL,
  chat_type TEXT NULL,
  message TEXT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  CONSTRAINT incoming_webhook_logs_pkey PRIMARY KEY (id),
  CONSTRAINT incoming_webhook_logs_client_id_fkey FOREIGN KEY (client_id) REFERENCES clients (client_id)
);

-- Essential indexes for webhook log queries
CREATE INDEX IF NOT EXISTS idx_incoming_webhook_logs_client_id ON public.incoming_webhook_logs (client_id);
CREATE INDEX IF NOT EXISTS idx_incoming_webhook_logs_created_at ON public.incoming_webhook_logs (created_at DESC);
CREATE INDEX IF NOT EXISTS idx_incoming_webhook_logs_platform ON public.incoming_webhook_logs (platform) WHERE platform IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_incoming_webhook_logs_event_type ON public.incoming_webhook_logs (event_type) WHERE event_type IS NOT NULL;
CREATE TABLE public.error_trigger_logs (
  id SERIAL NOT NULL,
  workflow TEXT NULL,
  url TEXT NULL,
  node TEXT NULL,
  error_message TEXT NULL,
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  CONSTRAINT error_trigger_logs_pkey PRIMARY KEY (id)
);

-- Useful indexes for workflow debugging
CREATE INDEX IF NOT EXISTS idx_error_trigger_logs_workflow ON public.error_trigger_logs (workflow) WHERE workflow IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_error_trigger_logs_timestamp ON public.error_trigger_logs (timestamp DESC);

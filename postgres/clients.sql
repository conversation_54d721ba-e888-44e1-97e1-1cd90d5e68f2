
CREATE SEQUENCE IF NOT EXISTS client_id_seq;

-- ALTER SEQUENCE client_id_seq RESTART WITH 1;

CREATE TABLE public.clients (
  auth_id uuid NOT NULL,
  client_id text NOT NULL DEFAULT (
    'CB'::text || lpad(
      (nextval('client_id_seq'::regclass))::text,
      4,
      '0'::text
    )
  ),
  username text NOT NULL,
  company_name text NULL,
  contact_email text NULL,
  contact_phone text NULL,
  created_at timestamp with time zone NULL DEFAULT now(),
  updated_at timestamp with time zone NULL DEFAULT now(),
  status text NULL DEFAULT 'active'::text,
  created_by text NULL,
  sector text NULL,
  lang text NULL,
  plan_type text NULL,
  billing_cycle text NULL,
  start_date DATE NULL,
  next_billing_date DATE NULL,
  lang_2 text[] NULL,
  usage_used integer NULL,
  usage_limit integer NULL,
  contact_need text NULL,
  CONSTRAINT clients_pkey PRIMARY KEY (auth_id),
  CONSTRAINT clients_client_id_key UNIQUE (client_id),
  CONSTRAINT clients_username_key UNIQUE (username)
);


CREATE TRIGGER trg_update_clients_timestamp 
BEFORE UPDATE ON clients 
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
CREATE TABLE public.client_credentials (
  id serial NOT NULL,
  client_id text NOT NULL,
  fb_url text NULL,
  fb_token text NULL,
  ig_url text NULL,
  ig_token text NULL,
  tg_url text NULL,
  tg_token text NULL,
  wa_url text NULL,
  wa_token text NULL,
  web_url text NULL,
  updated_at timestamp with time zone NULL DEFAULT now(),
  created_at timestamp with time zone NULL DEFAULT now(),
  tg_id text NULL,
  tg_name text NULL,
  tg_id_name text NULL,
  tg_status smallint NULL DEFAULT '0'::smallint,
  ig_status smallint NULL DEFAULT '0'::smallint,
  fb_status smallint NULL DEFAULT '0'::smallint,
  wa_status smallint NULL DEFAULT '0'::smallint,
  web_status smallint NULL DEFAULT '0'::smallint,
  web_domain text NULL,
  fb_id text NULL,
  fb_name text NULL,
  ig_id text NULL,
  ig_name text NULL,
  wa_id text NULL,
  wa_name text NULL,
  web_name text NULL,
  tg_biz_id text NULL,
  client_tg text NULL,
  CONSTRAINT client_credentials_pkey PRIMARY KEY (id),
  CONSTRAINT unique_fb_id UNIQUE (fb_id),
  CONSTRAINT unique_ig_id UNIQUE (ig_id),
  CONSTRAINT unique_tg_id UNIQUE (tg_id),
  CONSTRAINT unique_wa_id UNIQUE (wa_id),
  CONSTRAINT client_credentials_client_id_fkey FOREIGN KEY (client_id) REFERENCES clients (client_id)
);

CREATE INDEX IF NOT EXISTS idx_client_credentials_client_id ON public.client_credentials USING btree (client_id);
CREATE INDEX IF NOT EXISTS idx_client_credentials_client_tg ON public.client_credentials USING btree (client_tg);

CREATE TRIGGER trg_update_client_credentials_timestamp 
BEFORE UPDATE ON client_credentials 
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
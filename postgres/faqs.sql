-- Enable vector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create FAQs table
CREATE TABLE public.faqs (
  id SERIAL NOT NULL,
  client_id text NOT NULL,
  question TEXT NULL,
  question_p TEXT NULL,
  answer TEXT NULL,
  answer_p TEXT NULL,
  audio_url TEXT NULL,
  photo_url text[] NULL,
  photo_id TEXT NULL,
  audio_duration INTEGER NULL,
  audio_file_path TEXT NULL,
  updated_at TIMESTAMP WITH TIME ZONE NULL DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE NULL DEFAULT now(),
  faq_id TEXT NULL,
  fb_photo_atmid text[] NULL,
  fb_audio_atmid TEXT NULL,
  tg_photo_atmid text[] NULL,
  tg_audio_atmid TEXT NULL,
  ig_photo_atmid text[] NULL,
  ig_audio_atmid TEXT NULL,
  is_visible BOOLEAN NULL,
  embedding VECTOR(512) NULL,
  
  CONSTRAINT faqs_pkey PRIMARY KEY (id),
  CONSTRAINT faqs_client_id_fkey FOREIGN KEY (client_id) REFERENCES clients (client_id)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_faqs_client_id ON public.faqs (client_id);
CREATE INDEX IF NOT EXISTS idx_faqs_client_visible ON public.faqs (client_id, is_visible) WHERE is_visible = true;
-- Vector similarity search index (ESSENTIAL for performance)
CREATE INDEX IF NOT EXISTS idx_faqs_embedding ON public.faqs USING hnsw (embedding vector_cosine_ops);

-- Add trigger for auto-updating updated_at
CREATE TRIGGER trg_update_faqs_timestamp 
  BEFORE UPDATE ON public.faqs 
  FOR EACH ROW 
  EXECUTE FUNCTION public.update_modified_column();

-- RAG search function - searches across ALL variants for specific client
CREATE OR REPLACE FUNCTION fetch_similar_faqs (
  query_embedding vector(512),
  client_id text,
  match_count INT DEFAULT 3
)
RETURNS TABLE (
  question TEXT,
  question_p TEXT,
  answer TEXT,
  answer_p TEXT,
  audio_url TEXT,
  photo_url text[],
  audio_duration INT,
  fb_photo_atmid text[],
  fb_audio_atmid TEXT,
  tg_photo_atmid text[],
  tg_audio_atmid TEXT,
  ig_photo_atmid text[],
  ig_audio_atmid TEXT,
  faq_id TEXT,
  similarity FLOAT
)
LANGUAGE plpgsql
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
    SELECT
      f.question,
      f.question_p,
      f.answer,
      f.answer_p,
      f.audio_url,
      f.photo_url,
      f.audio_duration,
      f.fb_photo_atmid,
      f.fb_audio_atmid,
      f.tg_photo_atmid,
      f.tg_audio_atmid,
      f.ig_photo_atmid,
      f.ig_audio_atmid,
      f.faq_id,
      1 - (f.embedding <=> query_embedding) AS similarity
    FROM public.faqs f
    WHERE f.client_id = client_id
      AND f.embedding IS NOT NULL
    ORDER BY f.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- Example usage:
-- SELECT * FROM fetch_similar_faqs('[0.1,0.2,0.3,...]'::vector(512), 'your-uuid-here'::uuid, 5);
# Admin Dashboard

A Next.js 15.1.0 TypeScript admin dashboard for LLM analytics and user management.

## Features

- 🔐 **Authentication** - Supabase Auth with protected routes
- 📊 **Analytics Dashboard** - Usage metrics and cost analysis with charts
- 👥 **User Management** - Add users and resolve user issues
- 🤖 **Model Management** - CRUD operations for LLM models and pricing
- 📄 **Request Logs** - Detailed logs with filtering and pagination
- 🎨 **Modern UI** - shadcn/ui components with dark mode support
- 🔗 **N8N Integration** - All external data via webhook endpoints
- ⚡ **Performance** - Built with Next.js 15.1 and React 18

## Tech Stack

- **Framework**: Next.js 15.1.0 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **UI Components**: shadcn/ui
- **Charts**: Recharts
- **Authentication**: Supabase Auth
- **Database**: Supabase (primary) + N8N webhooks for external data
- **Icons**: Lucide React

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- Supabase account and project
- N8N instance with configured webhooks

### Installation

1. **Clone and install dependencies**
   ```bash
   cd admin-dashboard
   npm install
   ```

2. **Set up environment variables**
   
   Copy `.env.local` and fill in your values:
   ```bash
   # Supabase Configuration
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

   # N8N Webhook URLs
   MODEL_WEBHOOK_URL=your_model_webhook_url
   REQUEST_LOG_WEBHOOK_URL=your_request_log_webhook_url
   USER_WEBHOOK_URL=your_user_webhook_url
   ```

3. **Set up Supabase Authentication**
   
   In your Supabase dashboard:
   - Enable Email authentication
   - Create an admin user account
   - Configure any additional auth settings

4. **Configure N8N Webhooks**
   
   Set up these webhook endpoints in your N8N instance:
   - `MODEL_WEBHOOK_URL` - For model CRUD operations
   - `REQUEST_LOG_WEBHOOK_URL` - For analytics data
   - `USER_WEBHOOK_URL` - For user management actions

5. **Run the development server**
   ```bash
   npm run dev
   ```

   Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
src/
├── app/
│   ├── (auth)/
│   │   └── login/                 # Authentication pages
│   ├── (dashboard)/
│   │   ├── analytics/             # Analytics dashboard
│   │   ├── models/                # Model management
│   │   ├── users/                 # User management
│   │   └── logs/                  # Request logs
│   └── api/
│       ├── n8n/                   # N8N webhook API routes
│       └── analytics/             # Analytics API routes
├── components/
│   ├── ui/                        # shadcn/ui components
│   ├── layout/                    # Layout components
│   ├── tables/                    # Data table components
│   ├── charts/                    # Chart components
│   ├── forms/                     # Form components
│   └── providers/                 # Context providers
├── lib/
│   ├── supabase.ts               # Supabase client configuration
│   ├── webhooks.ts               # N8N webhook utilities
│   └── utils.ts                  # Utility functions
└── types/
    └── index.ts                  # TypeScript type definitions
```

## Key Features

### Authentication & Security
- Supabase Auth integration
- Protected routes middleware
- Server-side authentication checks
- Session management

### Analytics Dashboard
- Overview metrics (requests, costs, averages)
- Daily charts for requests and costs
- Recent activity logs
- Date range filtering
- Real-time data via N8N webhooks

### User Management
- Add new users with initial settings
- User problem resolution actions:
  - Upgrade plan
  - Add messages
  - Reset password
  - Clear user data
- All actions trigger N8N webhooks

### Model Management
- CRUD operations for LLM models
- Pricing configuration per token type
- Provider and model filtering
- Real-time updates

### Request Logs
- Paginated log table
- Advanced filtering options
- Detailed log viewer
- Export functionality (placeholder)
- Cost and token breakdown

### UI/UX
- Responsive design
- Dark/light mode toggle
- Loading states
- Error handling
- Toast notifications

## API Routes

### N8N Integration
- `POST /api/n8n/users` - Add new user
- `POST /api/n8n/users/actions` - User problem resolution
- `GET /api/n8n/models` - Fetch models
- `POST /api/n8n/models` - Add model
- `PUT /api/n8n/models/[key]` - Update model
- `DELETE /api/n8n/models/[key]` - Delete model

### Analytics
- `GET /api/analytics/overview` - Dashboard metrics
- `GET /api/analytics/logs` - Request logs with pagination

## Data Flow

1. **Authentication**: Supabase handles user auth and sessions
2. **Dashboard Data**: Fetched from N8N webhooks with caching
3. **User Actions**: Sent to N8N webhooks for processing
4. **Model Management**: Direct CRUD via N8N model webhook
5. **Analytics**: Real-time data from N8N request log webhook

## Deployment

### Build the application
```bash
npm run build
```

### Environment Variables
Ensure all environment variables are set in your deployment platform.

### Database Setup
- Configure Supabase project
- Set up authentication providers
- Create admin user accounts

### N8N Configuration
- Deploy N8N workflows
- Configure webhook endpoints
- Test webhook connectivity

## Development

### Adding New Features
1. Create new page in appropriate `(dashboard)` subfolder
2. Add API routes in `api/` if needed
3. Create reusable components in `components/`
4. Update types in `types/index.ts`

### Styling
- Uses Tailwind CSS v4 with custom CSS variables
- shadcn/ui components provide consistent design system
- Dark mode support via CSS variables

### State Management
- React Context for auth and theme
- Server state via React Query (if needed)
- Local state with React hooks

## Contributing

1. Follow the existing code structure
2. Use TypeScript for all new code
3. Add proper error handling
4. Update types as needed
5. Test authentication flows
6. Verify webhook integrations

## Security Considerations

- All webhook calls are server-side only
- Environment variables for sensitive data
- Input validation for all API routes
- Protected routes with authentication checks
- Rate limiting considerations for webhook calls

## License

MIT License - see LICENSE file for details.
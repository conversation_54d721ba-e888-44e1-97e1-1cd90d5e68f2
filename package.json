{"name": "admin-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.844.0", "@aws-sdk/s3-request-presigner": "^3.844.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.3", "@types/jsonwebtoken": "^9.0.10", "@types/pg": "^8.15.4", "class-variance-authority": "^0.7.1", "claude-code": "^0.0.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "jose": "^6.0.12", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "15.2.3", "pg": "^8.16.3", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "recharts": "^3.0.2", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^3.25.74"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "^15.2.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}